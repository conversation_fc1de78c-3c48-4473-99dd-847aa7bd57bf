import Foundation
import SwiftUI

struct FileContentView: View {
    let file: FileItem
    let reloadID: UUID  // External reload trigger
    @State private var fileContent: String = ""
    @State private var image: NSImage? = nil
    @State private var isTextFile: Bool = true
    @State private var lastModificationDate: Date = Date()
    @State private var refreshTimer: Timer?

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text(file.name)
                    .font(.headline)
                Spacer()
                Button(action: reload) {
                    Image(systemName: "arrow.clockwise")
                }
                .help("Reload from disk")
            }
            .padding(.bottom, 4)

            Divider()

            if isTextFile {
                ScrollView {
                    Text(fileContent)
                        .font(.system(.body, design: .monospaced))
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            } else if let image = image {
                ScrollView([.horizontal, .vertical]) {
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .padding()
                }
            } else {
                Text("Unable to display file content.")
                    .foregroundColor(.secondary)
                    .padding()
            }
        }
        .padding()
        .onAppear {
            loadFileContent()
            startFileMonitoring()
        }
        .onDisappear {
            stopFileMonitoring()
        }
        .onChange(of: reloadID) { _ in
            // SIMPLE FIX: Reload content whenever reloadID changes
            print("🔄 FileContentView: External reload triggered for \(file.name)")
            loadFileContent()
        }

        .id(reloadID)  // Forces view reload when reloadID changes
    }

    private func loadFileContent() {
        let fileExtension = URL(fileURLWithPath: file.path).pathExtension.lowercased()
        isTextFile = [
            "txt", "md", "swift", "java", "c", "cpp", "h", "hpp", "py", "js", "html", "css", "xml",
            "json",
        ].contains(fileExtension)
        let url = URL(fileURLWithPath: file.path)

        // CRITICAL FIX: Clear any cached content first to ensure fresh load
        fileContent = ""
        image = nil

        // ENHANCED: Force clear any potential system-level file caching
        // This ensures we always read the latest content from disk
        let _ = FileManager.default.contents(atPath: file.path)  // Prime the cache with latest content

        // Add debug logging to track file content loading
        print("FileContentView: Loading content for \(url.lastPathComponent) (loadID: \(loadID))")

        if isTextFile {
            // Use FileManager to ensure we get the latest file content
            if FileManager.default.fileExists(atPath: file.path),
                let data = try? Data(contentsOf: url),
                let content = String(data: data, encoding: .utf8)
            {
                fileContent = content
                print(
                    "FileContentView: Loaded \(content.count) characters from \(url.lastPathComponent)"
                )
            } else {
                fileContent = "(Unable to load file content)"
                print("FileContentView: Failed to load content from \(url.lastPathComponent)")
            }
        } else {
            if FileManager.default.fileExists(atPath: file.path),
                let data = try? Data(contentsOf: url),
                let img = NSImage(data: data)
            {
                image = img
                print("FileContentView: Loaded image from \(url.lastPathComponent)")
            } else {
                print("FileContentView: Failed to load image from \(url.lastPathComponent)")
            }
            fileContent = ""
        }
    }

    func reload() {
        // Change loadID to force .id() to reload the view
        loadID = UUID()
        loadFileContent()
    }

    // CRITICAL FIX: Monitor file for changes to detect version restoration
    private func startFileMonitoring() {
        // Get initial modification date
        if let attributes = try? FileManager.default.attributesOfItem(atPath: file.path),
            let modDate = attributes[.modificationDate] as? Date
        {
            lastModificationDate = modDate
        }

        // SIMPLE FIX: Check for file changes every 0.1 seconds for immediate restoration detection
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            checkForFileChanges()
        }
    }

    private func stopFileMonitoring() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }

    private func checkForFileChanges() {
        guard let attributes = try? FileManager.default.attributesOfItem(atPath: file.path),
            let modDate = attributes[.modificationDate] as? Date
        else {
            return
        }

        // SIMPLE FIX: If file was modified, reload content immediately
        if modDate > lastModificationDate {
            print("🔄 FileContentView: File \(file.name) changed, reloading")
            lastModificationDate = modDate
            DispatchQueue.main.async {
                self.loadFileContent()
            }
        }
    }

}
