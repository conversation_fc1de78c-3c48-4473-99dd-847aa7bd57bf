import Foundation
import SwiftUI

struct FileContentView: View {
    let file: FileItem
    @State private var fileContent: String = ""
    @State private var image: NSImage? = nil
    @State private var isTextFile: Bool = true
    @State private var loadID = UUID()  // Used to force reload
    @State private var lastModificationDate: Date = Date()
    @State private var refreshTimer: Timer?

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text(file.name)
                    .font(.headline)
                Spacer()
                Button(action: reload) {
                    Image(systemName: "arrow.clockwise")
                }
                .help("Reload from disk")
            }
            .padding(.bottom, 4)

            Divider()

            if isTextFile {
                ScrollView {
                    Text(fileContent)
                        .font(.system(.body, design: .monospaced))
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            } else if let image = image {
                ScrollView([.horizontal, .vertical]) {
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .padding()
                }
            } else {
                Text("Unable to display file content.")
                    .foregroundColor(.secondary)
                    .padding()
            }
        }
        .padding()
        .onAppear {
            loadFileContent()
            startFileMonitoring()
        }
        .onDisappear {
            stopFileMonitoring()
        }
        .onReceive(
            NotificationCenter.default.publisher(
                for: NSNotification.Name("FileRestoredNotification"))
        ) { notification in
            // CRITICAL FIX: Handle file restoration notification for immediate content refresh
            print("🔔 FileContentView: Received FileRestoredNotification")
            print("🔔 FileContentView: Current file: \(file.path)")
            print(
                "🔔 FileContentView: Notification object: \(String(describing: notification.object))"
            )

            if let restoredFilePath = notification.object as? String {
                print("🔔 FileContentView: Restored file path: \(restoredFilePath)")
                if restoredFilePath == file.path {
                    print("🔔 FileContentView: Path match! Reloading content")
                    // Force immediate content reload with a slight delay to ensure file system has updated
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                        self.loadID = UUID()  // Force view refresh
                        self.loadFileContent()
                    }
                } else {
                    print("🔔 FileContentView: Path mismatch - ignoring notification")
                }
            } else {
                print("🔔 FileContentView: Invalid notification object")
            }
        }
        .id(loadID)  // Forces view reload when loadID changes
    }

    private func loadFileContent() {
        let fileExtension = URL(fileURLWithPath: file.path).pathExtension.lowercased()
        isTextFile = [
            "txt", "md", "swift", "java", "c", "cpp", "h", "hpp", "py", "js", "html", "css", "xml",
            "json",
        ].contains(fileExtension)
        let url = URL(fileURLWithPath: file.path)

        // CRITICAL FIX: Clear any cached content first to ensure fresh load
        fileContent = ""
        image = nil

        // ENHANCED: Force clear any potential system-level file caching
        // This ensures we always read the latest content from disk
        let _ = FileManager.default.contents(atPath: file.path)  // Prime the cache with latest content

        // Add debug logging to track file content loading
        print("FileContentView: Loading content for \(url.lastPathComponent) (loadID: \(loadID))")

        if isTextFile {
            // Use FileManager to ensure we get the latest file content
            if FileManager.default.fileExists(atPath: file.path),
                let data = try? Data(contentsOf: url),
                let content = String(data: data, encoding: .utf8)
            {
                fileContent = content
                print(
                    "FileContentView: Loaded \(content.count) characters from \(url.lastPathComponent)"
                )
            } else {
                fileContent = "(Unable to load file content)"
                print("FileContentView: Failed to load content from \(url.lastPathComponent)")
            }
        } else {
            if FileManager.default.fileExists(atPath: file.path),
                let data = try? Data(contentsOf: url),
                let img = NSImage(data: data)
            {
                image = img
                print("FileContentView: Loaded image from \(url.lastPathComponent)")
            } else {
                print("FileContentView: Failed to load image from \(url.lastPathComponent)")
            }
            fileContent = ""
        }
    }

    func reload() {
        // Change loadID to force .id() to reload the view
        loadID = UUID()
        loadFileContent()
    }

    // CRITICAL FIX: Monitor file for changes to detect version restoration
    private func startFileMonitoring() {
        // Get initial modification date
        if let attributes = try? FileManager.default.attributesOfItem(atPath: file.path),
            let modDate = attributes[.modificationDate] as? Date
        {
            lastModificationDate = modDate
        }

        // ENHANCED: Reduced timer interval for faster detection of file changes during restoration
        // Start a timer to check for file changes every 0.2 seconds for better responsiveness
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 0.2, repeats: true) { _ in
            checkForFileChanges()
        }
    }

    private func stopFileMonitoring() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }

    private func checkForFileChanges() {
        guard let attributes = try? FileManager.default.attributesOfItem(atPath: file.path),
            let modDate = attributes[.modificationDate] as? Date
        else {
            return
        }

        // ENHANCED: More precise file change detection for restoration scenarios
        // If file was modified after our last known modification date, reload content
        if modDate > lastModificationDate {
            lastModificationDate = modDate
            // ENHANCED: Force view refresh and reload content immediately
            // This ensures that file restoration changes are immediately visible
            DispatchQueue.main.async {
                self.loadID = UUID()  // Force view refresh
                self.loadFileContent()
            }
        }
    }
}
