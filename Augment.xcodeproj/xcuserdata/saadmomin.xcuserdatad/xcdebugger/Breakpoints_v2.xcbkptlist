<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "CCA10C1E-68AC-40B2-ADC9-C4535BC18D32"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DCA74738-4305-409D-AAAF-61B7A8F0FC8D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Augment/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "382"
            endingLineNumber = "382"
            landmarkName = "deleteSpace(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
