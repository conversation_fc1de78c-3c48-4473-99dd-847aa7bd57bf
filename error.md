FileSystemMonitor: Initialized
[DEBUG] loaded space: name=Documents, mode=manual
[DEBUG] loaded space: name=manual, mode=manual
[DEBUG] loaded space: name=manual1, mode=manual
[DEBUG] loaded space: name=manual3, mode=manual
[DEBUG] loaded space: name=jdjw, mode=manual
[DEBUG] loaded space: name=ldmc, mode=manual
[DEBUG] loaded space: name=dsd, mode=manual
[DEBUG] loaded space: name=adsx, mode=manual
[DEBUG] loaded space: name=new, mode=manual
[DEBUG] loaded space: name=dsfd, mode=manual
[DEBUG] loaded space: name=auto, mode=auto
[DEBUG] loaded space: name=kam, mode=auto
[DEBUG] loaded space: name=hkqk, mode=auto
[DEBUG] loaded space: name=efeff, mode=auto
[DEBUG] loaded space: name=vfdbf, mode=auto
[DEBUG] loaded space: name=zzzz, mode=auto
[DEBUG] loaded space: name=jnzjln, mode=auto
[DEBUG] loaded space: name=alala, mode=auto
[DEBUG] loaded space: name=qkqqkq, mode=auto
[DEBUG] loaded space: name=aa, mode=manual
[DEBUG] loaded space: name=vfff, mode=manual
[DEBUG] loaded space: name=dvsv, mode=auto
[DEBUG] loaded space: name=sxxsc, mode=auto
[DEBUG] loaded space: name=fbdb, mode=auto
[DEBUG] loaded space: name=dwee, mode=auto
[DEBUG] loaded space: name=qmqmq, mode=auto
[DEBUG] loaded space: name=sasz, mode=auto
[DEBUG] loaded space: name=smamam, mode=auto
[DEBUG] loaded space: name=3m3m, mode=auto
[DEBUG] loaded space: name=saass, mode=auto
[DEBUG] loaded space: name=ssss, mode=auto
[DEBUG] loaded space: name=aaaa, mode=manual
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space Documents at /Users/<USER>/Library/Containers/com.augment.Augment/Data/Documents
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual at /Users/<USER>/Documents/demo_manual
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual1 at /Users/<USER>/Documents/demo_auto copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual3 at /Users/<USER>/Documents/demo_auto copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jdjw at /Users/<USER>/Documents/demo_manual copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space ldmc at /Users/<USER>/Documents/demo_manual copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsd at /Users/<USER>/Documents/demo_manual copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space adsx at /Users/<USER>/Documents/demo_manual copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space new at /Users/<USER>/Documents/demo_manual copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsfd at /Users/<USER>/Documents/demo_manual copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space auto at /Users/<USER>/Documents/demo_manual copy 7
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space kam at /Users/<USER>/Documents/demo_manual copy 8
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space hkqk at /Users/<USER>/Documents/demo_manual copy 9
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space efeff at /Users/<USER>/Documents/demo_auto copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfdbf at /Users/<USER>/Documents/demo_auto copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space zzzz at /Users/<USER>/Documents/demo_auto copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jnzjln at /Users/<USER>/Documents/demo_auto copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space alala at /Users/<USER>/Downloads/djdjdj
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qkqqkq at /Users/<USER>/Downloads/akmkm
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space aa at /Users/<USER>/Downloads/ajubi
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfff at /Users/<USER>/Downloads/djsnckd
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dvsv at /Users/<USER>/Desktop/untitled folder 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sxxsc at /Users/<USER>/Desktop/untitled folder 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space fbdb at /Users/<USER>/Desktop/untitled folder 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dwee at /Users/<USER>/Desktop/untitled folder 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qmqmq at /Users/<USER>/Desktop/untitled folder 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sasz at /Users/<USER>/Desktop/untitled folder 7
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space smamam at /Users/<USER>/Desktop/untitled folder 8
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space 3m3m at /Users/<USER>/Desktop/akak
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space saass at /Users/<USER>/Desktop/untitled folder 9
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space ssss at /Users/<USER>/Desktop/untitled folder 10
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space aaaa at /Users/<USER>/Desktop/untitled folder 11
FileSystemMonitor: Initialized
FileOperationInterceptor: Initialized and ready for event handling
cannot open file at line 49448 of [1b37c146ee]
os_unix.c:49448: (2) open(/private/var/db/DetachedSignatures) - No such file or directory
ViewBridge to RemoteViewService Terminated: Error Domain=com.apple.ViewBridge Code=18 "(null)" UserInfo={com.apple.ViewBridge.error.hint=this process disconnected remote view controller -- benign unless unexpected, com.apple.ViewBridge.error.description=NSViewBridgeErrorCanceled}
[DEBUG] createSpace: name=smz, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 12/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 12/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 12/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 12/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 12/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 12
Successfully created file version A9F5F271-3A81-4A36-8AA5-2BA10A0196DB for test_content.txt
✅ FileSystemMonitor: Added valid space 'smz' at /Users/<USER>/Desktop/untitled folder 12
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 1 paths
ContentView: Successfully created space 'smz' at '/Users/<USER>/Desktop/untitled folder 12'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space smz at /Users/<USER>/Desktop/untitled folder 12
FileSystemMonitor: Processing 4 events with enhanced safety
FileSystemMonitor: Processing 4 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Switching to fallback mode due to FSEvents corruption
🔄 FileSystemMonitor: Starting fallback mode with periodic scanning
✅ FileSystemMonitor: Fallback mode started successfully
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (227.160747051239s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 12 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 12
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata/6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
FileBrowserView: Started monitoring space smz
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
Unable to open mach-O at path: default.metallib  Error:2
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
GenerativeModelsAvailability.Parameters: Initialized with invalid language code: en-IN. Expected to receive two-letter ISO 639 code. e.g. 'zh' or 'en'. Falling back to: en
AFIsDeviceGreymatterEligible Missing entitlements for os_eligibility lookup
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (232.16257894039154s), skipping
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 12 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 12
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata/6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space smz
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔍 FileSystemMonitor: Space: smz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt in space: smz
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 12/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 12
Successfully created file version 07DAD41E-E00A-47B4-9B95-B61FFABE9751 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 12/test_content.txt in space: smz
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 12/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 12
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 12 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 12
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata/6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json (extension: json)
   📄 File: 07DAD41E-E00A-47B4-9B95-B61FFABE9751.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB
🔄 MetadataManager.loadFileVersionMetadata: Processing 07DAD41E-E00A-47B4-9B95-B61FFABE9751.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 07DAD41E-E00A-47B4-9B95-B61FFABE9751
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 07DAD41E-E00A-47B4-9B95-B61FFABE9751, Timestamp: 2025-07-12 09:41:24 +0000, Comment: No comment
   2. ID: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB, Timestamp: 2025-07-12 09:41:13 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 07DAD41E-E00A-47B4-9B95-B61FFABE9751
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["07DAD41E", "A9F5F271"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 07DAD41E
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 07DAD41E
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 07DAD41E
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 07DAD41E
🎨 VersionBrowser.ForEach: **CREATING ROW** for version A9F5F271
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for A9F5F271
🎨 VersionBrowser.ForEach: **CREATING ROW** for version A9F5F271
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for A9F5F271
🎯 VersionBrowser: Selected version A9F5F271
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["07DAD41E", "A9F5F271"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 07DAD41E
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 07DAD41E
🎨 VersionBrowser.ForEach: **CREATING ROW** for version A9F5F271
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for A9F5F271
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (7.627688050270081s), skipping
🔄 VersionBrowser: Starting restoration of version A9F5F271-3A81-4A36-8AA5-2BA10A0196DB
🔄 VersionBrowser: File path: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 12/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 12
Successfully created file version 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0 for test_content.txt
Created backup version: 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0
Successfully restored file test_content.txt to version A9F5F271-3A81-4A36-8AA5-2BA10A0196DB
🔄 VersionBrowser: Restoration result: true
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 12/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 12
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 12 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 12
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata/6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0.json (extension: json)
   📄 File: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json (extension: json)
   📄 File: 07DAD41E-E00A-47B4-9B95-B61FFABE9751.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0
🔄 MetadataManager.loadFileVersionMetadata: Processing A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB
🔄 MetadataManager.loadFileVersionMetadata: Processing 07DAD41E-E00A-47B4-9B95-B61FFABE9751.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 07DAD41E-E00A-47B4-9B95-B61FFABE9751
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0, Timestamp: 2025-07-12 09:41:31 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 3:11 PM
   2. ID: 07DAD41E-E00A-47B4-9B95-B61FFABE9751, Timestamp: 2025-07-12 09:41:24 +0000, Comment: No comment
   3. ID: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB, Timestamp: 2025-07-12 09:41:13 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["2D5A2DA0", "07DAD41E", "A9F5F271"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 07DAD41E
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 07DAD41E
🎨 VersionBrowser.ForEach: **CREATING ROW** for version A9F5F271
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for A9F5F271
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 2D5A2DA0
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 2D5A2DA0
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 2D5A2DA0
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 2D5A2DA0
🔄 VersionBrowser: Updated file modification time to trigger FileContentView reload
🔴 VersionHistoryView.sheet: Close button clicked
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 12 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 12
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 12/.augment/file_metadata/6ff0d4c2944c545a25c6bb0d93b883cd03ce3d49ef5f66fd8416713aac213a5c
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0.json (extension: json)
   📄 File: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json (extension: json)
   📄 File: 07DAD41E-E00A-47B4-9B95-B61FFABE9751.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 2D5A2DA0-F3A9-47D8-90A6-1D9670C145E0
🔄 MetadataManager.loadFileVersionMetadata: Processing A9F5F271-3A81-4A36-8AA5-2BA10A0196DB.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: A9F5F271-3A81-4A36-8AA5-2BA10A0196DB
🔄 MetadataManager.loadFileVersionMetadata: Processing 07DAD41E-E00A-47B4-9B95-B61FFABE9751.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 07DAD41E-E00A-47B4-9B95-B61FFABE9751
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
FileBrowserView: Started monitoring space smz
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔍 FileSystemMonitor: Space: smz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 12/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt in space: smz
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 12/test_content.txt (total suppressed: 0)
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (7.481479048728943s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (12.482390999794006s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (17.48275399208069s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (22.482524037361145s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (27.48415195941925s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (32.48175799846649s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (37.48255097866058s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (42.48072803020477s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (47.48196995258331s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (52.4818229675293s), skipping
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (57.481611013412476s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 12/.DS_Store
🔍 FileSystemMonitor: Space: smz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 12/.DS_Store
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 12/.DS_Store
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 12/.DS_Store in space: smz
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 12/.DS_Store
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 12
Successfully created file version E60E7032-2F36-43E4-8244-6AA0F3AD2398 for .DS_Store
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 12/.DS_Store in space: smz
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (62.49552798271179s), skipping
FileSystemMonitor: Processing 4 events with enhanced safety
FileSystemMonitor: Processing 4 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 12/.DS_Store
🔍 FileSystemMonitor: Space: smz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 12/.DS_Store
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 12/.DS_Store
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 12/.DS_Store in space: smz
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 12/.DS_Store
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 12
Successfully created file version 1F2EEF0D-DF2F-4133-AC0E-4A077770AD8E for .DS_Store
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 12/.DS_Store in space: smz
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (67.48734295368195s), skipping
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (6.8666030168533325s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (72.481006026268s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (11.865786075592041s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (77.48006498813629s), skipping
ContentView: Space with name 'zzzz' already exists
ContentView: Space with name 'zzzz' already exists
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (16.865462064743042s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (82.48002099990845s), skipping
ContentView: Space with name 'zzzz' already exists
ContentView: Space with name 'zzzz' already exists
ContentView: Space with name 'zzzz' already exists
ContentView: Space with name 'zzzz' already exists
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (21.865557074546814s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (87.47980105876923s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (26.95826506614685s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (92.57245004177094s), skipping
ContentView: Space with name 'zzzz' already exists
ContentView: Space with name 'zzzz' already exists
ContentView: Space with name 'zzzz' already exists
ContentView: Space with name 'zzzz' already exists
ContentView: Space with name 'zzzz' already exists
[DEBUG] createSpace: name=zzzz3, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 13/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 13/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 13/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 13/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 13/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 13
Successfully created file version D83EDC71-5549-4F28-A07D-313B565FAE66 for test_content.txt
✅ FileSystemMonitor: Added valid space 'zzzz3' at /Users/<USER>/Desktop/untitled folder 13
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 2 paths
ContentView: Successfully created space 'zzzz3' at '/Users/<USER>/Desktop/untitled folder 13'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space zzzz3 at /Users/<USER>/Desktop/untitled folder 13
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (31.891772985458374s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (97.50584697723389s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 13/test_content.txt modified too long ago (699.2316650152206s), skipping
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 13 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 13
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata/7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
FileBrowserView: Started monitoring space zzzz3
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: D83EDC71-5549-4F28-A07D-313B565FAE66.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing D83EDC71-5549-4F28-A07D-313B565FAE66.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D83EDC71-5549-4F28-A07D-313B565FAE66
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space smz
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (37.8102570772171s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (103.42453503608704s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 13/test_content.txt modified too long ago (705.1513420343399s), skipping
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (41.86737298965454s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (107.48160195350647s), skipping
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔍 FileSystemMonitor: Space: zzzz3, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt in space: zzzz3
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 13/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 13
Successfully created file version 780F7802-5304-490F-9493-B8C83C9B553D for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 13/test_content.txt in space: zzzz3
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 13 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 13
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata/7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 780F7802-5304-490F-9493-B8C83C9B553D.json (extension: json)
   📄 File: D83EDC71-5549-4F28-A07D-313B565FAE66.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 780F7802-5304-490F-9493-B8C83C9B553D.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 780F7802-5304-490F-9493-B8C83C9B553D
🔄 MetadataManager.loadFileVersionMetadata: Processing D83EDC71-5549-4F28-A07D-313B565FAE66.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D83EDC71-5549-4F28-A07D-313B565FAE66
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space zzzz3
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 13/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 13
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 13 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 13
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata/7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 780F7802-5304-490F-9493-B8C83C9B553D.json (extension: json)
   📄 File: D83EDC71-5549-4F28-A07D-313B565FAE66.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 780F7802-5304-490F-9493-B8C83C9B553D.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 780F7802-5304-490F-9493-B8C83C9B553D
🔄 MetadataManager.loadFileVersionMetadata: Processing D83EDC71-5549-4F28-A07D-313B565FAE66.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D83EDC71-5549-4F28-A07D-313B565FAE66
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 780F7802-5304-490F-9493-B8C83C9B553D, Timestamp: 2025-07-12 09:43:19 +0000, Comment: No comment
   2. ID: D83EDC71-5549-4F28-A07D-313B565FAE66, Timestamp: 2025-07-12 09:43:09 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 780F7802-5304-490F-9493-B8C83C9B553D
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["780F7802", "D83EDC71"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 780F7802
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 780F7802
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 780F7802
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 780F7802
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D83EDC71
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D83EDC71
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D83EDC71
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D83EDC71
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (46.865488052368164s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (112.47989404201508s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 13/test_content.txt modified too long ago (5.817474961280823s), skipping
🎯 VersionBrowser: Selected version D83EDC71
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["780F7802", "D83EDC71"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 780F7802
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 780F7802
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D83EDC71
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D83EDC71
🎯 VersionBrowser: Selected version D83EDC71
🔄 VersionBrowser: Starting restoration of version D83EDC71-5549-4F28-A07D-313B565FAE66
🔄 VersionBrowser: File path: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 13/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 13
Successfully created file version 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD for test_content.txt
Created backup version: 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD
Successfully restored file test_content.txt to version D83EDC71-5549-4F28-A07D-313B565FAE66
🔄 VersionBrowser: Restoration result: true
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 13/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 13
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 13 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 13
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata/7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD.json (extension: json)
   📄 File: 780F7802-5304-490F-9493-B8C83C9B553D.json (extension: json)
   📄 File: D83EDC71-5549-4F28-A07D-313B565FAE66.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD
🔄 MetadataManager.loadFileVersionMetadata: Processing 780F7802-5304-490F-9493-B8C83C9B553D.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 780F7802-5304-490F-9493-B8C83C9B553D
🔄 MetadataManager.loadFileVersionMetadata: Processing D83EDC71-5549-4F28-A07D-313B565FAE66.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D83EDC71-5549-4F28-A07D-313B565FAE66
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD, Timestamp: 2025-07-12 09:43:27 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 3:13 PM
   2. ID: 780F7802-5304-490F-9493-B8C83C9B553D, Timestamp: 2025-07-12 09:43:19 +0000, Comment: No comment
   3. ID: D83EDC71-5549-4F28-A07D-313B565FAE66, Timestamp: 2025-07-12 09:43:09 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["4ACAB403", "780F7802", "D83EDC71"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 780F7802
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 780F7802
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D83EDC71
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D83EDC71
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4ACAB403
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4ACAB403
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4ACAB403
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4ACAB403
🔄 VersionBrowser: Updated file modification time to trigger FileContentView reload
🔴 VersionHistoryView.sheet: Close button clicked
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (51.86346900463104s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (117.47761106491089s), skipping
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔍 FileSystemMonitor: Space: zzzz3, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 13/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt in space: zzzz3
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 13 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 13
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 13/.augment/file_metadata/7be8cce80602e000af4a6ee83264c1420f616099caa432e36c2a2291aec562db
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD.json (extension: json)
   📄 File: 780F7802-5304-490F-9493-B8C83C9B553D.json (extension: json)
   📄 File: D83EDC71-5549-4F28-A07D-313B565FAE66.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 4ACAB403-62B5-496B-BEF4-4E3EA68C3ACD
🔄 MetadataManager.loadFileVersionMetadata: Processing 780F7802-5304-490F-9493-B8C83C9B553D.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 780F7802-5304-490F-9493-B8C83C9B553D
🔄 MetadataManager.loadFileVersionMetadata: Processing D83EDC71-5549-4F28-A07D-313B565FAE66.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D83EDC71-5549-4F28-A07D-313B565FAE66
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
FileBrowserView: Started monitoring space zzzz3
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 13/test_content.txt (total suppressed: 0)
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (56.86449205875397s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (122.47894704341888s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 13/test_content.txt modified too long ago (6.319936990737915s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (61.86450397968292s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (127.4789810180664s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 13/test_content.txt modified too long ago (11.319987058639526s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/.DS_Store modified too long ago (66.86459600925446s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 12/test_content.txt modified too long ago (132.47909796237946s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 13/test_content.txt modified too long ago (16.320464968681335s), skipping