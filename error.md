FileSystemMonitor: Initialized
[DEBUG] loaded space: name=Documents, mode=manual
[DEBUG] loaded space: name=manual, mode=manual
[DEBUG] loaded space: name=manual1, mode=manual
[DEBUG] loaded space: name=manual3, mode=manual
[DEBUG] loaded space: name=jdjw, mode=manual
[DEBUG] loaded space: name=ldmc, mode=manual
[DEBUG] loaded space: name=dsd, mode=manual
[DEBUG] loaded space: name=adsx, mode=manual
[DEBUG] loaded space: name=new, mode=manual
[DEBUG] loaded space: name=dsfd, mode=manual
[DEBUG] loaded space: name=auto, mode=auto
[DEBUG] loaded space: name=kam, mode=auto
[DEBUG] loaded space: name=hkqk, mode=auto
[DEBUG] loaded space: name=efeff, mode=auto
[DEBUG] loaded space: name=vfdbf, mode=auto
[DEBUG] loaded space: name=zzzz, mode=auto
[DEBUG] loaded space: name=jnzjln, mode=auto
[DEBUG] loaded space: name=alala, mode=auto
[DEBUG] loaded space: name=qkqqkq, mode=auto
[DEBUG] loaded space: name=aa, mode=manual
[DEBUG] loaded space: name=vfff, mode=manual
[DEBUG] loaded space: name=dvsv, mode=auto
[DEBUG] loaded space: name=sxxsc, mode=auto
[DEBUG] loaded space: name=fbdb, mode=auto
[DEBUG] loaded space: name=dwee, mode=auto
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space Documents at /Users/<USER>/Library/Containers/com.augment.Augment/Data/Documents
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual at /Users/<USER>/Documents/demo_manual
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual1 at /Users/<USER>/Documents/demo_auto copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual3 at /Users/<USER>/Documents/demo_auto copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jdjw at /Users/<USER>/Documents/demo_manual copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space ldmc at /Users/<USER>/Documents/demo_manual copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsd at /Users/<USER>/Documents/demo_manual copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space adsx at /Users/<USER>/Documents/demo_manual copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space new at /Users/<USER>/Documents/demo_manual copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsfd at /Users/<USER>/Documents/demo_manual copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space auto at /Users/<USER>/Documents/demo_manual copy 7
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space kam at /Users/<USER>/Documents/demo_manual copy 8
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space hkqk at /Users/<USER>/Documents/demo_manual copy 9
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space efeff at /Users/<USER>/Documents/demo_auto copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfdbf at /Users/<USER>/Documents/demo_auto copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space zzzz at /Users/<USER>/Documents/demo_auto copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jnzjln at /Users/<USER>/Documents/demo_auto copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space alala at /Users/<USER>/Downloads/djdjdj
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qkqqkq at /Users/<USER>/Downloads/akmkm
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space aa at /Users/<USER>/Downloads/ajubi
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfff at /Users/<USER>/Downloads/djsnckd
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dvsv at /Users/<USER>/Desktop/untitled folder 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sxxsc at /Users/<USER>/Desktop/untitled folder 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space fbdb at /Users/<USER>/Desktop/untitled folder 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dwee at /Users/<USER>/Desktop/untitled folder 5
FileSystemMonitor: Initialized
FileOperationInterceptor: Initialized and ready for event handling
cannot open file at line 49448 of [1b37c146ee]
os_unix.c:49448: (2) open(/private/var/db/DetachedSignatures) - No such file or directory
ViewBridge to RemoteViewService Terminated: Error Domain=com.apple.ViewBridge Code=18 "(null)" UserInfo={com.apple.ViewBridge.error.hint=this process disconnected remote view controller -- benign unless unexpected, com.apple.ViewBridge.error.description=NSViewBridgeErrorCanceled}
[DEBUG] createSpace: name=qmqmq, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 6/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 6/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 6/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 6/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 6
Successfully created file version 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E for test_content.txt
✅ FileSystemMonitor: Added valid space 'qmqmq' at /Users/<USER>/Desktop/untitled folder 6
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 1 paths
ContentView: Successfully created space 'qmqmq' at '/Users/<USER>/Desktop/untitled folder 6'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qmqmq at /Users/<USER>/Desktop/untitled folder 6
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Switching to fallback mode due to FSEvents corruption
🔄 FileSystemMonitor: Starting fallback mode with periodic scanning
✅ FileSystemMonitor: Fallback mode started successfully
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 6/test_content.txt modified too long ago (337.6316479444504s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 6 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 6
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata/90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
FileBrowserView: Started monitoring space qmqmq
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
Unable to open mach-O at path: default.metallib  Error:2
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
GenerativeModelsAvailability.Parameters: Initialized with invalid language code: en-IN. Expected to receive two-letter ISO 639 code. e.g. 'zh' or 'en'. Falling back to: en
AFIsDeviceGreymatterEligible Missing entitlements for os_eligibility lookup
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 6/test_content.txt modified too long ago (342.6403639316559s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 6 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 6
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata/90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space qmqmq
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔍 FileSystemMonitor: Space: qmqmq, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt in space: qmqmq
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 6
Successfully created file version 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 6/test_content.txt in space: qmqmq
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 6 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 6
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata/90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
FileBrowserView: Started monitoring space qmqmq
   📄 File: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json (extension: json)
   📄 File: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3
🔄 MetadataManager.loadFileVersionMetadata: Processing 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 6 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 6
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata/90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json (extension: json)
   📄 File: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3
🔄 MetadataManager.loadFileVersionMetadata: Processing 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space qmqmq
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 6/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 6
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 6 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 6
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata/90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json (extension: json)
   📄 File: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3
🔄 MetadataManager.loadFileVersionMetadata: Processing 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3, Timestamp: 2025-07-12 09:11:52 +0000, Comment: No comment
   2. ID: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E, Timestamp: 2025-07-12 09:11:41 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["4353C775", "03EBE3D3"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4353C775
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4353C775
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4353C775
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4353C775
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 03EBE3D3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 03EBE3D3
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 03EBE3D3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 03EBE3D3
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 6/test_content.txt modified too long ago (7.789263010025024s), skipping
🎯 VersionBrowser: Selected version 03EBE3D3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["4353C775", "03EBE3D3"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4353C775
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4353C775
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 03EBE3D3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 03EBE3D3
🎯 VersionBrowser: Selected version 03EBE3D3
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 6/test_content.txt modified too long ago (12.792687058448792s), skipping
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 6
Successfully created file version 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D for test_content.txt
Created backup version: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D
Successfully restored file test_content.txt to version 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 6/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 6
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 6 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 6
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata/90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json (extension: json)
   📄 File: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json (extension: json)
   📄 File: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3
🔄 MetadataManager.loadFileVersionMetadata: Processing 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
🔄 MetadataManager.loadFileVersionMetadata: Processing 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D, Timestamp: 2025-07-12 09:12:03 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 2:41 PM
   2. ID: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3, Timestamp: 2025-07-12 09:11:52 +0000, Comment: No comment
   3. ID: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E, Timestamp: 2025-07-12 09:11:41 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["8DD1FE74", "4353C775", "03EBE3D3"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4353C775
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4353C775
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 03EBE3D3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 03EBE3D3
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 8DD1FE74
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 8DD1FE74
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 8DD1FE74
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 8DD1FE74
🔄 VersionBrowser: Calling onFileRestored callback
🔄 SpaceDetailView: onFileRestored callback triggered
📢 SpaceDetailView: Posted FileRestoredNotification
🔴 VersionHistoryView.sheet: Close button clicked
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 6 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 6
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata/90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json (extension: json)
   📄 File: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json (extension: json)
   📄 File: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3
🔄 MetadataManager.loadFileVersionMetadata: Processing 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
🔄 MetadataManager.loadFileVersionMetadata: Processing 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
FileBrowserView: Started monitoring space qmqmq
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔍 FileSystemMonitor: Space: qmqmq, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 6/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt in space: qmqmq
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 6/test_content.txt (total suppressed: 0)
FileBrowserView: Opening version history for test_content.txt
🎬 FileBrowserView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 6/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 6
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 6/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 6 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 6/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 6
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 6/.augment/file_metadata/90efaaa10a2d5451a869b6a205ba58c7a2431f56e987b55ccdff7dc498c530fe
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json (extension: json)
   📄 File: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json (extension: json)
   📄 File: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3
🔄 MetadataManager.loadFileVersionMetadata: Processing 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E
🔄 MetadataManager.loadFileVersionMetadata: Processing 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D, Timestamp: 2025-07-12 09:12:03 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 2:41 PM
   2. ID: 4353C775-3FC2-48B2-91E7-CFA8AD21BAB3, Timestamp: 2025-07-12 09:11:52 +0000, Comment: No comment
   3. ID: 03EBE3D3-6C3F-425F-B037-FD6A0BA9F06E, Timestamp: 2025-07-12 09:11:41 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 8DD1FE74-EFA9-477B-9E05-25F3EAD51B9D
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["8DD1FE74", "4353C775", "03EBE3D3"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 8DD1FE74
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 8DD1FE74
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 8DD1FE74
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 8DD1FE74
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4353C775
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4353C775
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4353C775
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4353C775
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 03EBE3D3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 03EBE3D3
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 03EBE3D3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 03EBE3D3
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 6/test_content.txt modified too long ago (9.583176970481873s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 6/test_content.txt modified too long ago (14.954604983329773s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 6/test_content.txt modified too long ago (19.58524799346924s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 6/test_content.txt modified too long ago (24.585062980651855s), skipping