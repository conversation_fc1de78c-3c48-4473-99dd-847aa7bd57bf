FileSystemMonitor: Scanning 0 spaces in fallback mode
FileSystemMonitor: Scanning 0 spaces in fallback mode
cannot open file at line 49448 of [1b37c146ee]
os_unix.c:49448: (2) open(/private/var/db/DetachedSignatures) - No such file or directory
ViewBridge to RemoteViewService Terminated: Error Domain=com.apple.ViewBridge Code=18 "(null)" UserInfo={com.apple.ViewBridge.error.hint=this process disconnected remote view controller -- benign unless unexpected, com.apple.ViewBridge.error.description=NSViewBridgeErrorCanceled}
[DEBUG] createSpace: name=saass, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 9/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 9/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 9/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 9/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 9/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 9
Successfully created file version D2C07E3E-CB0C-48D1-A630-4FC706067B7F for test_content.txt
✅ FileSystemMonitor: Added valid space 'saass' at /Users/<USER>/Desktop/untitled folder 9
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 1 paths
ContentView: Successfully created space 'saass' at '/Users/<USER>/Desktop/untitled folder 9'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space saass at /Users/<USER>/Desktop/untitled folder 9
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 9/test_content.txt modified too long ago (179.54710495471954s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 9 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 9
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata/79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
FileBrowserView: Started monitoring space saass
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: D2C07E3E-CB0C-48D1-A630-4FC706067B7F
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
Unable to open mach-O at path: default.metallib  Error:2
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
GenerativeModelsAvailability.Parameters: Initialized with invalid language code: en-IN. Expected to receive two-letter ISO 639 code. e.g. 'zh' or 'en'. Falling back to: en
AFIsDeviceGreymatterEligible Missing entitlements for os_eligibility lookup
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 9/test_content.txt modified too long ago (184.30985593795776s), skipping
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 9 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 9
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata/79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: D2C07E3E-CB0C-48D1-A630-4FC706067B7F
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space saass
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔍 FileSystemMonitor: Space: saass, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt in space: saass
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 9/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 9
Successfully created file version B5DF6C63-BFA0-443F-843B-4C6D52142C52 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 9/test_content.txt in space: saass
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 9/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 9
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 9 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 9
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata/79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json (extension: json)
   📄 File: B5DF6C63-BFA0-443F-843B-4C6D52142C52.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: D2C07E3E-CB0C-48D1-A630-4FC706067B7F
🔄 MetadataManager.loadFileVersionMetadata: Processing B5DF6C63-BFA0-443F-843B-4C6D52142C52.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: B5DF6C63-BFA0-443F-843B-4C6D52142C52
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: B5DF6C63-BFA0-443F-843B-4C6D52142C52, Timestamp: 2025-07-12 09:31:23 +0000, Comment: No comment
   2. ID: D2C07E3E-CB0C-48D1-A630-4FC706067B7F, Timestamp: 2025-07-12 09:31:13 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version B5DF6C63-BFA0-443F-843B-4C6D52142C52
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["B5DF6C63", "D2C07E3E"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version B5DF6C63
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for B5DF6C63
🎨 VersionBrowser.ForEach: **CREATING ROW** for version B5DF6C63
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for B5DF6C63
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D2C07E3E
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D2C07E3E
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D2C07E3E
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D2C07E3E
🎯 VersionBrowser: Selected version D2C07E3E
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["B5DF6C63", "D2C07E3E"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version B5DF6C63
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for B5DF6C63
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D2C07E3E
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D2C07E3E
🎯 VersionBrowser: Selected version D2C07E3E
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 9/test_content.txt modified too long ago (7.741773962974548s), skipping
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 9/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 9
Successfully created file version 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0 for test_content.txt
Created backup version: 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0
Successfully restored file test_content.txt to version D2C07E3E-CB0C-48D1-A630-4FC706067B7F
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 9/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 9
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 9 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 9
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata/79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json (extension: json)
   📄 File: 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0.json (extension: json)
   📄 File: B5DF6C63-BFA0-443F-843B-4C6D52142C52.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: D2C07E3E-CB0C-48D1-A630-4FC706067B7F
🔄 MetadataManager.loadFileVersionMetadata: Processing 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0
🔄 MetadataManager.loadFileVersionMetadata: Processing B5DF6C63-BFA0-443F-843B-4C6D52142C52.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: B5DF6C63-BFA0-443F-843B-4C6D52142C52
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
📢 VersionBrowser: Posted global FileWasRestored notification
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0, Timestamp: 2025-07-12 09:31:30 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 3:01 PM
   2. ID: B5DF6C63-BFA0-443F-843B-4C6D52142C52, Timestamp: 2025-07-12 09:31:23 +0000, Comment: No comment
   3. ID: D2C07E3E-CB0C-48D1-A630-4FC706067B7F, Timestamp: 2025-07-12 09:31:13 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["859C4AD8", "B5DF6C63", "D2C07E3E"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version B5DF6C63
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for B5DF6C63
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D2C07E3E
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D2C07E3E
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 859C4AD8
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 859C4AD8
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 859C4AD8
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 859C4AD8
🔴 VersionHistoryView.sheet: Close button clicked
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 9 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 9
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 9/.augment/file_metadata/79f0ca0c425d3bdec09d121c53a8d78e646c058268a76a1480bd96bd737214d9
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json (extension: json)
   📄 File: 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0.json (extension: json)
   📄 File: B5DF6C63-BFA0-443F-843B-4C6D52142C52.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing D2C07E3E-CB0C-48D1-A630-4FC706067B7F.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: D2C07E3E-CB0C-48D1-A630-4FC706067B7F
🔄 MetadataManager.loadFileVersionMetadata: Processing 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: 859C4AD8-C88D-48DF-A6EE-14D8F3DE10D0
🔄 MetadataManager.loadFileVersionMetadata: Processing B5DF6C63-BFA0-443F-843B-4C6D52142C52.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: B5DF6C63-BFA0-443F-843B-4C6D52142C52
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
FileBrowserView: Started monitoring space saass
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔍 FileSystemMonitor: Space: saass, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 9/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt in space: saass
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 9/test_content.txt (total suppressed: 0)
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 9/test_content.txt modified too long ago (8.638024926185608s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 9/test_content.txt modified too long ago (13.637743949890137s), skipping