FileSystemMonitor: Initialized
[DEBUG] loaded space: name=Documents, mode=manual
[DEBUG] loaded space: name=manual, mode=manual
[DEBUG] loaded space: name=manual1, mode=manual
[DEBUG] loaded space: name=manual3, mode=manual
[DEBUG] loaded space: name=jdjw, mode=manual
[DEBUG] loaded space: name=ldmc, mode=manual
[DEBUG] loaded space: name=dsd, mode=manual
[DEBUG] loaded space: name=adsx, mode=manual
[DEBUG] loaded space: name=new, mode=manual
[DEBUG] loaded space: name=dsfd, mode=manual
[DEBUG] loaded space: name=auto, mode=auto
[DEBUG] loaded space: name=kam, mode=auto
[DEBUG] loaded space: name=hkqk, mode=auto
[DEBUG] loaded space: name=efeff, mode=auto
[DEBUG] loaded space: name=vfdbf, mode=auto
[DEBUG] loaded space: name=zzzz, mode=auto
[DEBUG] loaded space: name=jnzjln, mode=auto
[DEBUG] loaded space: name=alala, mode=auto
[DEBUG] loaded space: name=qkqqkq, mode=auto
[DEBUG] loaded space: name=aa, mode=manual
[DEBUG] loaded space: name=vfff, mode=manual
[DEBUG] loaded space: name=dvsv, mode=auto
[DEBUG] loaded space: name=sxxsc, mode=auto
[DEBUG] loaded space: name=fbdb, mode=auto
[DEBUG] loaded space: name=dwee, mode=auto
[DEBUG] loaded space: name=qmqmq, mode=auto
[DEBUG] loaded space: name=sasz, mode=auto
[DEBUG] loaded space: name=smamam, mode=auto
[DEBUG] loaded space: name=3m3m, mode=auto
[DEBUG] loaded space: name=saass, mode=auto
[DEBUG] loaded space: name=ssss, mode=auto
[DEBUG] loaded space: name=aaaa, mode=manual
[DEBUG] loaded space: name=smz, mode=auto
[DEBUG] loaded space: name=zzzz3, mode=auto
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space Documents at /Users/<USER>/Library/Containers/com.augment.Augment/Data/Documents
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual at /Users/<USER>/Documents/demo_manual
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual1 at /Users/<USER>/Documents/demo_auto copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual3 at /Users/<USER>/Documents/demo_auto copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jdjw at /Users/<USER>/Documents/demo_manual copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space ldmc at /Users/<USER>/Documents/demo_manual copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsd at /Users/<USER>/Documents/demo_manual copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space adsx at /Users/<USER>/Documents/demo_manual copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space new at /Users/<USER>/Documents/demo_manual copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsfd at /Users/<USER>/Documents/demo_manual copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space auto at /Users/<USER>/Documents/demo_manual copy 7
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space kam at /Users/<USER>/Documents/demo_manual copy 8
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space hkqk at /Users/<USER>/Documents/demo_manual copy 9
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space efeff at /Users/<USER>/Documents/demo_auto copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfdbf at /Users/<USER>/Documents/demo_auto copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space zzzz at /Users/<USER>/Documents/demo_auto copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jnzjln at /Users/<USER>/Documents/demo_auto copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space alala at /Users/<USER>/Downloads/djdjdj
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qkqqkq at /Users/<USER>/Downloads/akmkm
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space aa at /Users/<USER>/Downloads/ajubi
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfff at /Users/<USER>/Downloads/djsnckd
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dvsv at /Users/<USER>/Desktop/untitled folder 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sxxsc at /Users/<USER>/Desktop/untitled folder 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space fbdb at /Users/<USER>/Desktop/untitled folder 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dwee at /Users/<USER>/Desktop/untitled folder 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qmqmq at /Users/<USER>/Desktop/untitled folder 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sasz at /Users/<USER>/Desktop/untitled folder 7
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space smamam at /Users/<USER>/Desktop/untitled folder 8
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space 3m3m at /Users/<USER>/Desktop/akak
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space saass at /Users/<USER>/Desktop/untitled folder 9
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space ssss at /Users/<USER>/Desktop/untitled folder 10
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space aaaa at /Users/<USER>/Desktop/untitled folder 11
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space smz at /Users/<USER>/Desktop/untitled folder 12
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space zzzz3 at /Users/<USER>/Desktop/untitled folder 13
FileSystemMonitor: Initialized
FileOperationInterceptor: Initialized and ready for event handling
cannot open file at line 49448 of [1b37c146ee]
os_unix.c:49448: (2) open(/private/var/db/DetachedSignatures) - No such file or directory
ViewBridge to RemoteViewService Terminated: Error Domain=com.apple.ViewBridge Code=18 "(null)" UserInfo={com.apple.ViewBridge.error.hint=this process disconnected remote view controller -- benign unless unexpected, com.apple.ViewBridge.error.description=NSViewBridgeErrorCanceled}
[DEBUG] createSpace: name=jwkwk, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 14/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 14/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 14/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 14/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 14
Successfully created file version E8B192CA-4B6B-4D23-80E1-6D7125739FA8 for test_content.txt
✅ FileSystemMonitor: Added valid space 'jwkwk' at /Users/<USER>/Desktop/untitled folder 14
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 1 paths
ContentView: Successfully created space 'jwkwk' at '/Users/<USER>/Desktop/untitled folder 14'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jwkwk at /Users/<USER>/Desktop/untitled folder 14
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Switching to fallback mode due to FSEvents corruption
🔄 FileSystemMonitor: Starting fallback mode with periodic scanning
✅ FileSystemMonitor: Fallback mode started successfully
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 14/test_content.txt modified too long ago (177.84188604354858s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 14 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 14
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata/86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
FileBrowserView: Started monitoring space jwkwk
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: E8B192CA-4B6B-4D23-80E1-6D7125739FA8
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
Unable to open mach-O at path: default.metallib  Error:2
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
GenerativeModelsAvailability.Parameters: Initialized with invalid language code: en-IN. Expected to receive two-letter ISO 639 code. e.g. 'zh' or 'en'. Falling back to: en
AFIsDeviceGreymatterEligible Missing entitlements for os_eligibility lookup
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 14/test_content.txt modified too long ago (182.84737503528595s), skipping
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 14 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 14
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata/86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: E8B192CA-4B6B-4D23-80E1-6D7125739FA8
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space jwkwk
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 14/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 14
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 14 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 14
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata/86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: E8B192CA-4B6B-4D23-80E1-6D7125739FA8
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 1 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 1 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 1 versions:
   1. ID: E8B192CA-4B6B-4D23-80E1-6D7125739FA8, Timestamp: 2025-07-12 09:46:24 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 1 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 1
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 1
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 1
🎯 VersionBrowser.loadVersions: Selected version E8B192CA-4B6B-4D23-80E1-6D7125739FA8
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 1
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 1
📋 VersionBrowser.UI: Version IDs: ["E8B192CA"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 1 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 1 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version E8B192CA
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for E8B192CA
🎨 VersionBrowser.ForEach: **CREATING ROW** for version E8B192CA
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for E8B192CA
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔍 FileSystemMonitor: Space: jwkwk, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt in space: jwkwk
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 14
Successfully created file version 4394E201-0365-4752-87B3-5C2A006A769B for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 14/test_content.txt in space: jwkwk
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 14/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 14
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 14 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 14
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata/86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json (extension: json)
   📄 File: 4394E201-0365-4752-87B3-5C2A006A769B.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: E8B192CA-4B6B-4D23-80E1-6D7125739FA8
🔄 MetadataManager.loadFileVersionMetadata: Processing 4394E201-0365-4752-87B3-5C2A006A769B.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 4394E201-0365-4752-87B3-5C2A006A769B
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 4394E201-0365-4752-87B3-5C2A006A769B, Timestamp: 2025-07-12 09:46:36 +0000, Comment: No comment
   2. ID: E8B192CA-4B6B-4D23-80E1-6D7125739FA8, Timestamp: 2025-07-12 09:46:24 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 1 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 4394E201-0365-4752-87B3-5C2A006A769B
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["4394E201", "E8B192CA"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version E8B192CA
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for E8B192CA
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4394E201
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4394E201
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4394E201
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4394E201
🎯 VersionBrowser: Selected version E8B192CA
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["4394E201", "E8B192CA"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4394E201
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4394E201
🎨 VersionBrowser.ForEach: **CREATING ROW** for version E8B192CA
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for E8B192CA
🔄 VersionBrowser: Starting restoration of version E8B192CA-4B6B-4D23-80E1-6D7125739FA8
🔄 VersionBrowser: File path: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 14
Successfully created file version 2829204D-63BF-4FCE-A21E-35087D5F3BF9 for test_content.txt
Created backup version: 2829204D-63BF-4FCE-A21E-35087D5F3BF9
Successfully restored file test_content.txt to version E8B192CA-4B6B-4D23-80E1-6D7125739FA8
🔄 VersionBrowser: Restoration result: true
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 14/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 14
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 14 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 14
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata/86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json (extension: json)
   📄 File: 2829204D-63BF-4FCE-A21E-35087D5F3BF9.json (extension: json)
   📄 File: 4394E201-0365-4752-87B3-5C2A006A769B.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: E8B192CA-4B6B-4D23-80E1-6D7125739FA8
🔄 MetadataManager.loadFileVersionMetadata: Processing 2829204D-63BF-4FCE-A21E-35087D5F3BF9.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 2829204D-63BF-4FCE-A21E-35087D5F3BF9
🔄 MetadataManager.loadFileVersionMetadata: Processing 4394E201-0365-4752-87B3-5C2A006A769B.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 4394E201-0365-4752-87B3-5C2A006A769B
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 2829204D-63BF-4FCE-A21E-35087D5F3BF9, Timestamp: 2025-07-12 09:46:41 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 3:16 PM
   2. ID: 4394E201-0365-4752-87B3-5C2A006A769B, Timestamp: 2025-07-12 09:46:36 +0000, Comment: No comment
   3. ID: E8B192CA-4B6B-4D23-80E1-6D7125739FA8, Timestamp: 2025-07-12 09:46:24 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 2829204D-63BF-4FCE-A21E-35087D5F3BF9
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔍 FileSystemMonitor: Space: jwkwk, Mode: auto
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 14/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
📋 VersionBrowser.UI: Version IDs: ["2829204D", "4394E201", "E8B192CA"]
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions

🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt in space: jwkwk
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4394E201
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4394E201
🎨 VersionBrowser.ForEach: **CREATING ROW** for version E8B192CA
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for E8B192CA
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 2829204D
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 2829204D
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 2829204D
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 2829204D
🔄 VersionBrowser: Updated file modification time to trigger FileContentView reload
🔴 VersionHistoryView.sheet: Close button clicked
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 14 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 14
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 14/.augment/file_metadata/86d925b489d513da022a6b002c02ae5fe94844bc652999c6494781b3d962de4e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json (extension: json)
   📄 File: 2829204D-63BF-4FCE-A21E-35087D5F3BF9.json (extension: json)
   📄 File: 4394E201-0365-4752-87B3-5C2A006A769B.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing E8B192CA-4B6B-4D23-80E1-6D7125739FA8.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: E8B192CA-4B6B-4D23-80E1-6D7125739FA8
🔄 MetadataManager.loadFileVersionMetadata: Processing 2829204D-63BF-4FCE-A21E-35087D5F3BF9.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 2829204D-63BF-4FCE-A21E-35087D5F3BF9
🔄 MetadataManager.loadFileVersionMetadata: Processing 4394E201-0365-4752-87B3-5C2A006A769B.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: 4394E201-0365-4752-87B3-5C2A006A769B
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
FileBrowserView: Started monitoring space jwkwk
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt (total suppressed: 0)
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔍 FileSystemMonitor: Space: jwkwk, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 14/test_content.txt in space: jwkwk
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 14/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 14
Successfully created file version 71A4E688-C18D-4EE9-AD4F-B8ECE96349F8 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 14/test_content.txt in space: jwkwk
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 14/test_content.txt modified too long ago (10.073819994926453s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 14/test_content.txt modified too long ago (14.597555041313171s), skipping