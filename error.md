[DEBUG] createSpace: name=dwee, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 5/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 5/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 5/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 5/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 5
Successfully created file version 4992CF2B-95BE-4533-BBC5-D2A6577ED61A for test_content.txt
✅ FileSystemMonitor: Added valid space 'dwee' at /Users/<USER>/Desktop/untitled folder 5
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 1 paths
ContentView: Successfully created space 'dwee' at '/Users/<USER>/Desktop/untitled folder 5'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dwee at /Users/<USER>/Desktop/untitled folder 5
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Switching to fallback mode due to FSEvents corruption
🔄 FileSystemMonitor: Starting fallback mode with periodic scanning
✅ FileSystemMonitor: Fallback mode started successfully
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 5/test_content.txt modified too long ago (500.9699560403824s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 5 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 5
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata/728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json
   ✅ Successfully read data (497 bytes)
FileBrowserView: Started monitoring space dwee
   ✅ Successfully decoded FileVersion: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
Unable to open mach-O at path: default.metallib  Error:2
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
GenerativeModelsAvailability.Parameters: Initialized with invalid language code: en-IN. Expected to receive two-letter ISO 639 code. e.g. 'zh' or 'en'. Falling back to: en
AFIsDeviceGreymatterEligible Missing entitlements for os_eligibility lookup
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 5/test_content.txt modified too long ago (505.9707520008087s), skipping
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 5 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 5
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata/728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space dwee
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 5/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 5
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 5 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 5
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata/728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 1 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 1 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 1 versions:
   1. ID: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A, Timestamp: 2025-07-12 09:05:48 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 1 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 1
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 1
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 1
🎯 VersionBrowser.loadVersions: Selected version 4992CF2B-95BE-4533-BBC5-D2A6577ED61A
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 1
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 1
📋 VersionBrowser.UI: Version IDs: ["4992CF2B"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 1 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 1 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4992CF2B
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4992CF2B
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4992CF2B
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4992CF2B
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔍 FileSystemMonitor: Space: dwee, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt in space: dwee
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 5
Successfully created file version 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 5/test_content.txt in space: dwee
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 5/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 5
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 5 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 5
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata/728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json (extension: json)
   📄 File: 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A
🔄 MetadataManager.loadFileVersionMetadata: Processing 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1, Timestamp: 2025-07-12 09:05:59 +0000, Comment: No comment
   2. ID: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A, Timestamp: 2025-07-12 09:05:48 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 1 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["65BED0DD", "4992CF2B"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4992CF2B
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4992CF2B
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 65BED0DD
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 65BED0DD
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 65BED0DD
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 65BED0DD
🎯 VersionBrowser: Selected version 4992CF2B
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["65BED0DD", "4992CF2B"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 65BED0DD
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 65BED0DD
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4992CF2B
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4992CF2B
🎯 VersionBrowser: Selected version 4992CF2B
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 5/test_content.txt modified too long ago (9.313451051712036s), skipping
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 5
Successfully created file version 4B56642D-14AF-4735-9D9C-F7225D3970E3 for test_content.txt
Created backup version: 4B56642D-14AF-4735-9D9C-F7225D3970E3
Successfully restored file test_content.txt to version 4992CF2B-95BE-4533-BBC5-D2A6577ED61A
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 5/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 5
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 5 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 5
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata/728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 4B56642D-14AF-4735-9D9C-F7225D3970E3.json (extension: json)
   📄 File: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json (extension: json)
   📄 File: 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4B56642D-14AF-4735-9D9C-F7225D3970E3.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: 4B56642D-14AF-4735-9D9C-F7225D3970E3
🔄 MetadataManager.loadFileVersionMetadata: Processing 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A
🔄 MetadataManager.loadFileVersionMetadata: Processing 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 4B56642D-14AF-4735-9D9C-F7225D3970E3, Timestamp: 2025-07-12 09:06:05 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 2:35 PM
   2. ID: 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1, Timestamp: 2025-07-12 09:05:59 +0000, Comment: No comment
   3. ID: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A, Timestamp: 2025-07-12 09:05:48 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 4B56642D-14AF-4735-9D9C-F7225D3970E3
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["4B56642D", "65BED0DD", "4992CF2B"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 65BED0DD
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 65BED0DD
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4992CF2B
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4992CF2B
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4B56642D
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4B56642D
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 4B56642D
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 4B56642D
🔄 VersionBrowser: Calling onFileRestored callback
🔄 SpaceDetailView: onFileRestored callback triggered
📢 SpaceDetailView: Posted FileRestoredNotification
🔴 VersionHistoryView.sheet: Close button clicked
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 5 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 5
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 5/.augment/file_metadata/728b496ab41b3f2e63547e7f7f2ce316221db46cc190a460dae265f0a14d6c1e
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 4B56642D-14AF-4735-9D9C-F7225D3970E3.json (extension: json)
   📄 File: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json (extension: json)
   📄 File: 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 4B56642D-14AF-4735-9D9C-F7225D3970E3.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: 4B56642D-14AF-4735-9D9C-F7225D3970E3
🔄 MetadataManager.loadFileVersionMetadata: Processing 4992CF2B-95BE-4533-BBC5-D2A6577ED61A.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 4992CF2B-95BE-4533-BBC5-D2A6577ED61A
🔄 MetadataManager.loadFileVersionMetadata: Processing 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 65BED0DD-F6BE-4FEA-A8E7-852DAC43DFB1
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
FileBrowserView: Started monitoring space dwee
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔍 FileSystemMonitor: Space: dwee, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 5/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt in space: dwee
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 5/test_content.txt (total suppressed: 0)
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 5/test_content.txt modified too long ago (9.003057956695557s), skipping
