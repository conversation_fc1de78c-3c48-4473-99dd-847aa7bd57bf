FileSystemMonitor: Initialized
[DEBUG] loaded space: name=Documents, mode=manual
[DEBUG] loaded space: name=manual, mode=manual
[DEBUG] loaded space: name=manual1, mode=manual
[DEBUG] loaded space: name=manual3, mode=manual
[DEBUG] loaded space: name=jdjw, mode=manual
[DEBUG] loaded space: name=ldmc, mode=manual
[DEBUG] loaded space: name=dsd, mode=manual
[DEBUG] loaded space: name=adsx, mode=manual
[DEBUG] loaded space: name=new, mode=manual
[DEBUG] loaded space: name=dsfd, mode=manual
[DEBUG] loaded space: name=auto, mode=auto
[DEBUG] loaded space: name=kam, mode=auto
[DEBUG] loaded space: name=hkqk, mode=auto
[DEBUG] loaded space: name=efeff, mode=auto
[DEBUG] loaded space: name=vfdbf, mode=auto
[DEBUG] loaded space: name=zzzz, mode=auto
[DEBUG] loaded space: name=jnzjln, mode=auto
[DEBUG] loaded space: name=alala, mode=auto
[DEBUG] loaded space: name=qkqqkq, mode=auto
[DEBUG] loaded space: name=aa, mode=manual
[DEBUG] loaded space: name=vfff, mode=manual
[DEBUG] loaded space: name=dvsv, mode=auto
[DEBUG] loaded space: name=sxxsc, mode=auto
[DEBUG] loaded space: name=fbdb, mode=auto
[DEBUG] loaded space: name=dwee, mode=auto
[DEBUG] loaded space: name=qmqmq, mode=auto
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space Documents at /Users/<USER>/Library/Containers/com.augment.Augment/Data/Documents
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual at /Users/<USER>/Documents/demo_manual
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual1 at /Users/<USER>/Documents/demo_auto copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual3 at /Users/<USER>/Documents/demo_auto copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jdjw at /Users/<USER>/Documents/demo_manual copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space ldmc at /Users/<USER>/Documents/demo_manual copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsd at /Users/<USER>/Documents/demo_manual copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space adsx at /Users/<USER>/Documents/demo_manual copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space new at /Users/<USER>/Documents/demo_manual copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsfd at /Users/<USER>/Documents/demo_manual copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space auto at /Users/<USER>/Documents/demo_manual copy 7
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space kam at /Users/<USER>/Documents/demo_manual copy 8
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space hkqk at /Users/<USER>/Documents/demo_manual copy 9
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space efeff at /Users/<USER>/Documents/demo_auto copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfdbf at /Users/<USER>/Documents/demo_auto copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space zzzz at /Users/<USER>/Documents/demo_auto copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jnzjln at /Users/<USER>/Documents/demo_auto copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space alala at /Users/<USER>/Downloads/djdjdj
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qkqqkq at /Users/<USER>/Downloads/akmkm
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space aa at /Users/<USER>/Downloads/ajubi
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfff at /Users/<USER>/Downloads/djsnckd
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dvsv at /Users/<USER>/Desktop/untitled folder 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sxxsc at /Users/<USER>/Desktop/untitled folder 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space fbdb at /Users/<USER>/Desktop/untitled folder 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dwee at /Users/<USER>/Desktop/untitled folder 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qmqmq at /Users/<USER>/Desktop/untitled folder 6
FileSystemMonitor: Initialized
FileOperationInterceptor: Initialized and ready for event handling
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Switching to fallback mode due to FSEvents corruption
🔄 FileSystemMonitor: Starting fallback mode with periodic scanning
✅ FileSystemMonitor: Fallback mode started successfully
FileSystemMonitor: Scanning 0 spaces in fallback mode
FileSystemMonitor: Scanning 0 spaces in fallback mode
cannot open file at line 49448 of [1b37c146ee]
os_unix.c:49448: (2) open(/private/var/db/DetachedSignatures) - No such file or directory
ViewBridge to RemoteViewService Terminated: Error Domain=com.apple.ViewBridge Code=18 "(null)" UserInfo={com.apple.ViewBridge.error.hint=this process disconnected remote view controller -- benign unless unexpected, com.apple.ViewBridge.error.description=NSViewBridgeErrorCanceled}
FileSystemMonitor: Scanning 0 spaces in fallback mode
[DEBUG] createSpace: name=sasz, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 7/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 7/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 7/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 7/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 7/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 7
Successfully created file version 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8 for test_content.txt
✅ FileSystemMonitor: Added valid space 'sasz' at /Users/<USER>/Desktop/untitled folder 7
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 1 paths
ContentView: Successfully created space 'sasz' at '/Users/<USER>/Desktop/untitled folder 7'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sasz at /Users/<USER>/Desktop/untitled folder 7
FileSystemMonitor: Processing 4 events with enhanced safety
FileSystemMonitor: Processing 4 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 7 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 7
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata/d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
FileBrowserView: Started monitoring space sasz
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
GenerativeModelsAvailability.Parameters: Initialized with invalid language code: en-IN. Expected to receive two-letter ISO 639 code. e.g. 'zh' or 'en'. Falling back to: en
AFIsDeviceGreymatterEligible Missing entitlements for os_eligibility lookup
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (684.3980309963226s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔍 FileSystemMonitor: Space: sasz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt in space: sasz
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 7/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 7
Successfully created file version 6B082D87-0E43-40DE-9D10-FC3D59F829F0 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 7/test_content.txt in space: sasz
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 7 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 7
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata/d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json (extension: json)
   📄 File: 6B082D87-0E43-40DE-9D10-FC3D59F829F0.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8
🔄 MetadataManager.loadFileVersionMetadata: Processing 6B082D87-0E43-40DE-9D10-FC3D59F829F0.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 6B082D87-0E43-40DE-9D10-FC3D59F829F0
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space sasz
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 7/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 7
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 7 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 7
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata/d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json (extension: json)
   📄 File: 6B082D87-0E43-40DE-9D10-FC3D59F829F0.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8
🔄 MetadataManager.loadFileVersionMetadata: Processing 6B082D87-0E43-40DE-9D10-FC3D59F829F0.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 6B082D87-0E43-40DE-9D10-FC3D59F829F0
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 6B082D87-0E43-40DE-9D10-FC3D59F829F0, Timestamp: 2025-07-12 09:23:31 +0000, Comment: No comment
   2. ID: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8, Timestamp: 2025-07-12 09:23:22 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 6B082D87-0E43-40DE-9D10-FC3D59F829F0
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["6B082D87", "14734FE6"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 6B082D87
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 6B082D87
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 6B082D87
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 6B082D87
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 14734FE6
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 14734FE6
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 14734FE6
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 14734FE6
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (6.261093020439148s), skipping
🎯 VersionBrowser: Selected version 14734FE6
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["6B082D87", "14734FE6"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 6B082D87
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 6B082D87
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 14734FE6
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 14734FE6
🎯 VersionBrowser: Selected version 14734FE6
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 7/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 7
Successfully created file version CC2B6758-E3C9-4283-91CF-7BDE256D5B87 for test_content.txt
Created backup version: CC2B6758-E3C9-4283-91CF-7BDE256D5B87
Successfully restored file test_content.txt to version 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 7/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 7
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 7 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 7
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata/d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json (extension: json)
   📄 File: CC2B6758-E3C9-4283-91CF-7BDE256D5B87.json (extension: json)
   📄 File: 6B082D87-0E43-40DE-9D10-FC3D59F829F0.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8
🔄 MetadataManager.loadFileVersionMetadata: Processing CC2B6758-E3C9-4283-91CF-7BDE256D5B87.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: CC2B6758-E3C9-4283-91CF-7BDE256D5B87
🔄 MetadataManager.loadFileVersionMetadata: Processing 6B082D87-0E43-40DE-9D10-FC3D59F829F0.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 6B082D87-0E43-40DE-9D10-FC3D59F829F0
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: CC2B6758-E3C9-4283-91CF-7BDE256D5B87, Timestamp: 2025-07-12 09:23:39 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 2:53 PM
   2. ID: 6B082D87-0E43-40DE-9D10-FC3D59F829F0, Timestamp: 2025-07-12 09:23:31 +0000, Comment: No comment
   3. ID: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8, Timestamp: 2025-07-12 09:23:22 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version CC2B6758-E3C9-4283-91CF-7BDE256D5B87
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["CC2B6758", "6B082D87", "14734FE6"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 6B082D87
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 6B082D87
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 14734FE6
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 14734FE6
🎨 VersionBrowser.ForEach: **CREATING ROW** for version CC2B6758
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for CC2B6758
🎨 VersionBrowser.ForEach: **CREATING ROW** for version CC2B6758
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for CC2B6758
🔴 VersionHistoryView.sheet: Close button clicked
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔍 FileSystemMonitor: Space: sasz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 7/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt in space: sasz
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 7 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 7
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 7/.augment/file_metadata/d8e4c70a6727887bb085890326d8860a04fe10114da14924f3a9860400befee4
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json (extension: json)
   📄 File: CC2B6758-E3C9-4283-91CF-7BDE256D5B87.json (extension: json)
   📄 File: 6B082D87-0E43-40DE-9D10-FC3D59F829F0.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 14734FE6-19A4-4E75-9A5F-9573A5A8C6A8
🔄 MetadataManager.loadFileVersionMetadata: Processing CC2B6758-E3C9-4283-91CF-7BDE256D5B87.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: CC2B6758-E3C9-4283-91CF-7BDE256D5B87
🔄 MetadataManager.loadFileVersionMetadata: Processing 6B082D87-0E43-40DE-9D10-FC3D59F829F0.json
   ✅ Successfully read data (461 bytes)
FileBrowserView: Started monitoring space sasz
   ✅ Successfully decoded FileVersion: 6B082D87-0E43-40DE-9D10-FC3D59F829F0
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 7/test_content.txt (total suppressed: 0)
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (7.057608962059021s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (12.057958006858826s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (17.058488965034485s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (22.05698597431183s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (27.057908058166504s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (32.056612968444824s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (37.05677604675293s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (42.05682599544525s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (47.05651497840881s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (52.05808401107788s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (57.05582296848297s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (62.0569189786911s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (67.05543303489685s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (72.05536699295044s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (77.07044506072998s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (82.05416595935822s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (87.0548700094223s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (92.05484104156494s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (97.05523800849915s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (102.05502498149872s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (107.05486702919006s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (112.05459296703339s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (117.05478298664093s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (122.0531519651413s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (127.05375099182129s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (132.05303597450256s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (137.05442702770233s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (142.05228507518768s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (147.05245399475098s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (152.05411803722382s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (157.05116605758667s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (162.05262804031372s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (167.0524230003357s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (172.05194997787476s), skipping
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (177.0519210100174s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Space: sasz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store in space: sasz
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 7/.DS_Store
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 7
Successfully created file version 344A2706-6563-463F-8220-B062C9D58593 for .DS_Store
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 7/.DS_Store in space: sasz
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (182.06130003929138s), skipping
FileSystemMonitor: Processing 4 events with enhanced safety
FileSystemMonitor: Processing 4 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
Unable to open mach-O at path: default.metallib  Error:2
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (9.971115946769714s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (187.05088698863983s), skipping
[DEBUG] createSpace: name=smamam, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 8/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 8/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 8/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 8/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 8
Successfully created file version 06600C85-4337-42E2-97B3-60C702F06E32 for test_content.txt
✅ FileSystemMonitor: Added valid space 'smamam' at /Users/<USER>/Desktop/untitled folder 8
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 2 paths
ContentView: Successfully created space 'smamam' at '/Users/<USER>/Desktop/untitled folder 8'
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (194.38336300849915s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (17.30402898788452s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (194.3837480545044s), skipping
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space smamam at /Users/<USER>/Desktop/untitled folder 8
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
FileBrowserView: Started monitoring space smamam
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space sasz
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (197.05166900157928s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (19.972672939300537s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (197.0529260635376s), skipping
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔍 FileSystemMonitor: Space: smamam, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt in space: smamam
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 8
Successfully created file version 32D9867C-A1C1-40EC-8671-FD1458BF669E for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 8/test_content.txt in space: smamam
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (24.980055928230286s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (202.060142993927s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
   📄 File: 32D9867C-A1C1-40EC-8671-FD1458BF669E.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
🔄 MetadataManager.loadFileVersionMetadata: Processing 32D9867C-A1C1-40EC-8671-FD1458BF669E.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 32D9867C-A1C1-40EC-8671-FD1458BF669E
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space smamam
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔍 FileSystemMonitor: Space: smamam, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt in space: smamam
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 8
Successfully created file version DEAC248C-24FD-4882-BB60-EF63E4C233A4 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 8/test_content.txt in space: smamam
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (29.977720022201538s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (207.05807304382324s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
   📄 File: 32D9867C-A1C1-40EC-8671-FD1458BF669E.json (extension: json)
   📄 File: DEAC248C-24FD-4882-BB60-EF63E4C233A4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
🔄 MetadataManager.loadFileVersionMetadata: Processing 32D9867C-A1C1-40EC-8671-FD1458BF669E.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 32D9867C-A1C1-40EC-8671-FD1458BF669E
🔄 MetadataManager.loadFileVersionMetadata: Processing DEAC248C-24FD-4882-BB60-EF63E4C233A4.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: DEAC248C-24FD-4882-BB60-EF63E4C233A4
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
FileBrowserView: Started monitoring space smamam
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
   📄 File: 32D9867C-A1C1-40EC-8671-FD1458BF669E.json (extension: json)
   📄 File: DEAC248C-24FD-4882-BB60-EF63E4C233A4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
🔄 MetadataManager.loadFileVersionMetadata: Processing 32D9867C-A1C1-40EC-8671-FD1458BF669E.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 32D9867C-A1C1-40EC-8671-FD1458BF669E
🔄 MetadataManager.loadFileVersionMetadata: Processing DEAC248C-24FD-4882-BB60-EF63E4C233A4.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: DEAC248C-24FD-4882-BB60-EF63E4C233A4
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space smamam
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 8/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 8
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
   📄 File: 32D9867C-A1C1-40EC-8671-FD1458BF669E.json (extension: json)
   📄 File: DEAC248C-24FD-4882-BB60-EF63E4C233A4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
🔄 MetadataManager.loadFileVersionMetadata: Processing 32D9867C-A1C1-40EC-8671-FD1458BF669E.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 32D9867C-A1C1-40EC-8671-FD1458BF669E
🔄 MetadataManager.loadFileVersionMetadata: Processing DEAC248C-24FD-4882-BB60-EF63E4C233A4.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: DEAC248C-24FD-4882-BB60-EF63E4C233A4
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: DEAC248C-24FD-4882-BB60-EF63E4C233A4, Timestamp: 2025-07-12 09:27:06 +0000, Comment: No comment
   2. ID: 32D9867C-A1C1-40EC-8671-FD1458BF669E, Timestamp: 2025-07-12 09:27:01 +0000, Comment: No comment
   3. ID: 06600C85-4337-42E2-97B3-60C702F06E32, Timestamp: 2025-07-12 09:26:54 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version DEAC248C-24FD-4882-BB60-EF63E4C233A4
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["DEAC248C", "32D9867C", "06600C85"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version DEAC248C
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for DEAC248C
🎨 VersionBrowser.ForEach: **CREATING ROW** for version DEAC248C
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for DEAC248C
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 32D9867C
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 32D9867C
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 32D9867C
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 32D9867C
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 06600C85
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 06600C85
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 06600C85
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 06600C85
🎯 VersionBrowser: Selected version 06600C85
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["DEAC248C", "32D9867C", "06600C85"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version DEAC248C
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for DEAC248C
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 32D9867C
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 32D9867C
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 06600C85
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 06600C85
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (8.053324937820435s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (34.972195982933044s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (212.05217397212982s), skipping
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 8
Successfully created file version AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6 for test_content.txt
Created backup version: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6
Successfully restored file test_content.txt to version 06600C85-4337-42E2-97B3-60C702F06E32
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 8/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 8
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 4 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
   📄 File: 32D9867C-A1C1-40EC-8671-FD1458BF669E.json (extension: json)
   📄 File: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6.json (extension: json)
   📄 File: DEAC248C-24FD-4882-BB60-EF63E4C233A4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
🔄 MetadataManager.loadFileVersionMetadata: Processing 32D9867C-A1C1-40EC-8671-FD1458BF669E.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 32D9867C-A1C1-40EC-8671-FD1458BF669E
🔄 MetadataManager.loadFileVersionMetadata: Processing AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6
🔄 MetadataManager.loadFileVersionMetadata: Processing DEAC248C-24FD-4882-BB60-EF63E4C233A4.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: DEAC248C-24FD-4882-BB60-EF63E4C233A4
📊 MetadataManager.loadFileVersionMetadata: Success: 4, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 4
✅ MetadataManager.loadFileVersionMetadata: Returning 4 sorted versions
📊 VersionControl.getVersions: Loaded 4 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 4 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 4 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 4 versions:
   1. ID: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6, Timestamp: 2025-07-12 09:27:14 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 2:56 PM
   2. ID: DEAC248C-24FD-4882-BB60-EF63E4C233A4, Timestamp: 2025-07-12 09:27:06 +0000, Comment: No comment
   3. ID: 32D9867C-A1C1-40EC-8671-FD1458BF669E, Timestamp: 2025-07-12 09:27:01 +0000, Comment: No comment
   4. ID: 06600C85-4337-42E2-97B3-60C702F06E32, Timestamp: 2025-07-12 09:26:54 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 4 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 4
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 3 to 4
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 4
🎯 VersionBrowser.loadVersions: Selected version AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 4
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 4
📋 VersionBrowser.UI: Version IDs: ["AD48F2AE", "DEAC248C", "32D9867C", "06600C85"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 4 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 4 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version DEAC248C
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for DEAC248C
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 32D9867C
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 32D9867C
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 06600C85
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 06600C85
🎨 VersionBrowser.ForEach: **CREATING ROW** for version AD48F2AE
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for AD48F2AE
🎨 VersionBrowser.ForEach: **CREATING ROW** for version AD48F2AE
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for AD48F2AE
🔴 VersionHistoryView.sheet: Close button clicked
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔍 FileSystemMonitor: Space: smamam, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 8/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt in space: smamam
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (39.97215402126312s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (217.05243706703186s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 4 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
   📄 File: 32D9867C-A1C1-40EC-8671-FD1458BF669E.json (extension: json)
   📄 File: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6.json (extension: json)
   📄 File: DEAC248C-24FD-4882-BB60-EF63E4C233A4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
🔄 MetadataManager.loadFileVersionMetadata: Processing 32D9867C-A1C1-40EC-8671-FD1458BF669E.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 32D9867C-A1C1-40EC-8671-FD1458BF669E
🔄 MetadataManager.loadFileVersionMetadata: Processing AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6
🔄 MetadataManager.loadFileVersionMetadata: Processing DEAC248C-24FD-4882-BB60-EF63E4C233A4.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: DEAC248C-24FD-4882-BB60-EF63E4C233A4
📊 MetadataManager.loadFileVersionMetadata: Success: 4, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 4
✅ MetadataManager.loadFileVersionMetadata: Returning 4 sorted versions
📊 VersionControl.getVersions: Loaded 4 versions for file: test_content.txt
FileBrowserView: Started monitoring space smamam
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 8/test_content.txt (total suppressed: 0)
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (7.281445026397705s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (44.9712039232254s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (222.05116498470306s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 4 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
   📄 File: 32D9867C-A1C1-40EC-8671-FD1458BF669E.json (extension: json)
   📄 File: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6.json (extension: json)
   📄 File: DEAC248C-24FD-4882-BB60-EF63E4C233A4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
🔄 MetadataManager.loadFileVersionMetadata: Processing 32D9867C-A1C1-40EC-8671-FD1458BF669E.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 32D9867C-A1C1-40EC-8671-FD1458BF669E
🔄 MetadataManager.loadFileVersionMetadata: Processing AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6.json
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6
🔄 MetadataManager.loadFileVersionMetadata: Processing DEAC248C-24FD-4882-BB60-EF63E4C233A4.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: DEAC248C-24FD-4882-BB60-EF63E4C233A4
📊 MetadataManager.loadFileVersionMetadata: Success: 4, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 4
✅ MetadataManager.loadFileVersionMetadata: Returning 4 sorted versions
📊 VersionControl.getVersions: Loaded 4 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space smamam
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 8/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 8 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 8/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 8
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 8/.augment/file_metadata/18f9efd68fcc99bbaeb53fa785a7902852a7ebf374c79af363c341836e35c632
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 4 files
   📄 File: 06600C85-4337-42E2-97B3-60C702F06E32.json (extension: json)
   📄 File: 32D9867C-A1C1-40EC-8671-FD1458BF669E.json (extension: json)
   📄 File: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6.json (extension: json)
   📄 File: DEAC248C-24FD-4882-BB60-EF63E4C233A4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 06600C85-4337-42E2-97B3-60C702F06E32.json
   ✅ Successfully read data (497 bytes)
   ✅ Successfully decoded FileVersion: 06600C85-4337-42E2-97B3-60C702F06E32
🔄 MetadataManager.loadFileVersionMetadata: Processing 32D9867C-A1C1-40EC-8671-FD1458BF669E.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: 32D9867C-A1C1-40EC-8671-FD1458BF669E
🔄 MetadataManager.loadFileVersionMetadata: Processing AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6.json
FileBrowserView: Started monitoring space smamam
   ✅ Successfully read data (528 bytes)
   ✅ Successfully decoded FileVersion: AD48F2AE-C16C-4CE9-A9B6-4A578C3ED4F6
🔄 MetadataManager.loadFileVersionMetadata: Processing DEAC248C-24FD-4882-BB60-EF63E4C233A4.json
   ✅ Successfully read data (461 bytes)
   ✅ Successfully decoded FileVersion: DEAC248C-24FD-4882-BB60-EF63E4C233A4
📊 MetadataManager.loadFileVersionMetadata: Success: 4, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 4
✅ MetadataManager.loadFileVersionMetadata: Returning 4 sorted versions
📊 VersionControl.getVersions: Loaded 4 versions for file: test_content.txt
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (12.281146049499512s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (49.97063195705414s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (227.05040407180786s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (17.280445098876953s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (54.96977400779724s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (232.0494520664215s), skipping
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (22.279524087905884s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (59.96888601779938s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (237.04858100414276s), skipping
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (27.280749082565308s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (64.97024202346802s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (242.0500580072403s), skipping
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (32.282371044158936s), skipping
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Space: sasz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store in space: sasz
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 7/.DS_Store
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 7
Successfully created file version FB745BEF-CFFC-4A52-9E7D-04422BF29223 for .DS_Store
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 7/.DS_Store in space: sasz
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (247.06056106090546s), skipping
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 8/.DS_Store
🔍 FileSystemMonitor: Space: smamam, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 8/.DS_Store
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 8/.DS_Store
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 8/.DS_Store in space: smamam
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 8/.DS_Store
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 8
Successfully created file version 22F3A8FB-FCFF-425F-BD48-8D856FBFE6A3 for .DS_Store
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 8/.DS_Store in space: smamam
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (37.28778100013733s), skipping
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Space: sasz, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 7/.DS_Store in space: sasz
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 7/.DS_Store
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 7
Successfully created file version DEA0F0E1-D022-4167-9BAD-B4194B13C07D for .DS_Store
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 7/.DS_Store in space: sasz
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (252.0630680322647s), skipping
FileSystemMonitor: Processing 6 events with enhanced safety
FileSystemMonitor: Processing 6 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
[DEBUG] createSpace: name=3m3m, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/akak/.augment
   📁 Folder versions: /Users/<USER>/Desktop/akak/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/akak/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/akak/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/akak/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/akak
Successfully created file version 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB for test_content.txt
✅ FileSystemMonitor: Added valid space '3m3m' at /Users/<USER>/Desktop/akak
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 3 paths
ContentView: Successfully created space '3m3m' at '/Users/<USER>/Desktop/akak'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space 3m3m at /Users/<USER>/Desktop/akak
FileSystemMonitor: Scanning 3 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/.DS_Store modified too long ago (9.252476811408997s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (42.418002009391785s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (9.25397539138794s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (257.18713200092316s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/akak/test_content.txt modified too long ago (42.41861402988434s), skipping
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/akak for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/akak/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/akak
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata/73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json
   ✅ Successfully read data (471 bytes)
   ✅ Successfully decoded FileVersion: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Started monitoring space 3m3m
FileBrowserView: Stopped monitoring space smamam
FileSystemMonitor: Scanning 3 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/.DS_Store modified too long ago (14.537737727165222s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (47.70350003242493s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (14.539658427238464s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (262.47308003902435s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/akak/test_content.txt modified too long ago (47.70511507987976s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/akak for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/akak/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/akak
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata/73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json
   ✅ Successfully read data (471 bytes)
   ✅ Successfully decoded FileVersion: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space 3m3m
FileSystemMonitor: Scanning 3 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/.DS_Store modified too long ago (19.11427676677704s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (52.28008306026459s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (19.11635446548462s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (267.04973804950714s), skipping
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/akak/test_content.txt
🔍 FileSystemMonitor: Space: 3m3m, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/akak/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/akak/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/akak/test_content.txt in space: 3m3m
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/akak
Successfully created file version 7A9C405A-FF71-4544-95D6-BD0D48F4A090 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/akak/test_content.txt in space: 3m3m
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/akak/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/akak/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/akak/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/akak
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/akak for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/akak/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/akak
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata/73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json (extension: json)
   📄 File: 7A9C405A-FF71-4544-95D6-BD0D48F4A090.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json
   ✅ Successfully read data (471 bytes)
   ✅ Successfully decoded FileVersion: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB
🔄 MetadataManager.loadFileVersionMetadata: Processing 7A9C405A-FF71-4544-95D6-BD0D48F4A090.json
   ✅ Successfully read data (435 bytes)
   ✅ Successfully decoded FileVersion: 7A9C405A-FF71-4544-95D6-BD0D48F4A090
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 7A9C405A-FF71-4544-95D6-BD0D48F4A090, Timestamp: 2025-07-12 09:28:06 +0000, Comment: No comment
   2. ID: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB, Timestamp: 2025-07-12 09:27:56 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 7A9C405A-FF71-4544-95D6-BD0D48F4A090
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["7A9C405A", "5E8D71D2"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 7A9C405A
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 7A9C405A
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 7A9C405A
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 7A9C405A
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5E8D71D2
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5E8D71D2
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5E8D71D2
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5E8D71D2
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/akak/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/akak/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/akak
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/akak for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/akak/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/akak
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata/73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json (extension: json)
   📄 File: 7A9C405A-FF71-4544-95D6-BD0D48F4A090.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json
   ✅ Successfully read data (471 bytes)
   ✅ Successfully decoded FileVersion: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB
🔄 MetadataManager.loadFileVersionMetadata: Processing 7A9C405A-FF71-4544-95D6-BD0D48F4A090.json
   ✅ Successfully read data (435 bytes)
   ✅ Successfully decoded FileVersion: 7A9C405A-FF71-4544-95D6-BD0D48F4A090
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 7A9C405A-FF71-4544-95D6-BD0D48F4A090, Timestamp: 2025-07-12 09:28:06 +0000, Comment: No comment
   2. ID: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB, Timestamp: 2025-07-12 09:27:56 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 7A9C405A-FF71-4544-95D6-BD0D48F4A090
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
FileSystemMonitor: Scanning 3 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/.DS_Store modified too long ago (24.115137815475464s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (57.280983090400696s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (24.117223381996155s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (272.05055797100067s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/akak/test_content.txt modified too long ago (7.093007922172546s), skipping
🎯 VersionBrowser: Selected version 5E8D71D2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["7A9C405A", "5E8D71D2"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 7A9C405A
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 7A9C405A
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5E8D71D2
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5E8D71D2
🎯 VersionBrowser: Selected version 5E8D71D2
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/akak/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/akak/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/akak
Successfully created file version 1A205C92-00CA-4C79-ACE7-EF3F80864DA4 for test_content.txt
Created backup version: 1A205C92-00CA-4C79-ACE7-EF3F80864DA4
Successfully restored file test_content.txt to version 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/akak/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/akak/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/akak
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/akak for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/akak/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/akak
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata/73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json (extension: json)
   📄 File: 7A9C405A-FF71-4544-95D6-BD0D48F4A090.json (extension: json)
   📄 File: 1A205C92-00CA-4C79-ACE7-EF3F80864DA4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json
   ✅ Successfully read data (471 bytes)
   ✅ Successfully decoded FileVersion: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB
🔄 MetadataManager.loadFileVersionMetadata: Processing 7A9C405A-FF71-4544-95D6-BD0D48F4A090.json
   ✅ Successfully read data (435 bytes)
   ✅ Successfully decoded FileVersion: 7A9C405A-FF71-4544-95D6-BD0D48F4A090
🔄 MetadataManager.loadFileVersionMetadata: Processing 1A205C92-00CA-4C79-ACE7-EF3F80864DA4.json
   ✅ Successfully read data (502 bytes)
   ✅ Successfully decoded FileVersion: 1A205C92-00CA-4C79-ACE7-EF3F80864DA4
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 1A205C92-00CA-4C79-ACE7-EF3F80864DA4, Timestamp: 2025-07-12 09:28:14 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 2:57 PM
   2. ID: 7A9C405A-FF71-4544-95D6-BD0D48F4A090, Timestamp: 2025-07-12 09:28:06 +0000, Comment: No comment
   3. ID: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB, Timestamp: 2025-07-12 09:27:56 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 1A205C92-00CA-4C79-ACE7-EF3F80864DA4
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["1A205C92", "7A9C405A", "5E8D71D2"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 7A9C405A
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 7A9C405A
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5E8D71D2
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5E8D71D2
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 1A205C92
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 1A205C92
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 1A205C92
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 1A205C92
🔴 VersionHistoryView.sheet: Close button clicked
FileSystemMonitor: Scanning 3 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/.DS_Store modified too long ago (29.113725781440735s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (62.27968406677246s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (29.11595046520233s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (277.04930102825165s), skipping
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/akak/.DS_Store
🔍 FileSystemMonitor: Space: 3m3m, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/akak/.DS_Store
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/akak/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/akak/.DS_Store
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/akak/.DS_Store in space: 3m3m
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/akak/.DS_Store
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/akak
Successfully created file version 785CFF45-A915-439F-9763-A0FA099B3E84 for .DS_Store
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/akak/.DS_Store in space: 3m3m
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/akak/test_content.txt
🔍 FileSystemMonitor: Space: 3m3m, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/akak/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/akak/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/akak/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/akak/test_content.txt in space: 3m3m
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/akak/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/akak for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/akak/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/akak
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/akak/.augment/file_metadata/73719c79403eba8dd2d2783f9c19c2855843ce9bbf521e8fea98a67ac90a17ac
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json (extension: json)
   📄 File: 7A9C405A-FF71-4544-95D6-BD0D48F4A090.json (extension: json)
   📄 File: 1A205C92-00CA-4C79-ACE7-EF3F80864DA4.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB.json
   ✅ Successfully read data (471 bytes)
   ✅ Successfully decoded FileVersion: 5E8D71D2-0BAB-44DF-9AE0-44879E4D61DB
🔄 MetadataManager.loadFileVersionMetadata: Processing 7A9C405A-FF71-4544-95D6-BD0D48F4A090.json
   ✅ Successfully read data (435 bytes)
   ✅ Successfully decoded FileVersion: 7A9C405A-FF71-4544-95D6-BD0D48F4A090
🔄 MetadataManager.loadFileVersionMetadata: Processing 1A205C92-00CA-4C79-ACE7-EF3F80864DA4.json
   ✅ Successfully read data (502 bytes)
   ✅ Successfully decoded FileVersion: 1A205C92-00CA-4C79-ACE7-EF3F80864DA4
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
FileBrowserView: Started monitoring space 3m3m
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/akak/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/akak/test_content.txt (total suppressed: 0)
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 3 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/.DS_Store modified too long ago (34.11364769935608s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (67.27931606769562s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (34.11537742614746s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (282.04862904548645s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/akak/.DS_Store modified too long ago (9.95483410358429s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/akak/test_content.txt modified too long ago (7.164139986038208s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 3 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/.DS_Store modified too long ago (39.11373281478882s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 8/test_content.txt modified too long ago (72.28003108501434s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/.DS_Store modified too long ago (39.116217374801636s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 7/test_content.txt modified too long ago (287.04944002628326s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/akak/.DS_Store modified too long ago (14.9553781747818s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/akak/test_content.txt modified too long ago (12.164494037628174s), skipping