FileSystemMonitor: Initialized
[DEBUG] loaded space: name=Documents, mode=manual
[DEBUG] loaded space: name=manual, mode=manual
[DEBUG] loaded space: name=manual1, mode=manual
[DEBUG] loaded space: name=manual3, mode=manual
[DEBUG] loaded space: name=jdjw, mode=manual
[DEBUG] loaded space: name=ldmc, mode=manual
[DEBUG] loaded space: name=dsd, mode=manual
[DEBUG] loaded space: name=adsx, mode=manual
[DEBUG] loaded space: name=new, mode=manual
[DEBUG] loaded space: name=dsfd, mode=manual
[DEBUG] loaded space: name=auto, mode=auto
[DEBUG] loaded space: name=kam, mode=auto
[DEBUG] loaded space: name=hkqk, mode=auto
[DEBUG] loaded space: name=efeff, mode=auto
[DEBUG] loaded space: name=vfdbf, mode=auto
[DEBUG] loaded space: name=zzzz, mode=auto
[DEBUG] loaded space: name=jnzjln, mode=auto
[DEBUG] loaded space: name=alala, mode=auto
[DEBUG] loaded space: name=qkqqkq, mode=auto
[DEBUG] loaded space: name=aa, mode=manual
[DEBUG] loaded space: name=vfff, mode=manual
[DEBUG] loaded space: name=dvsv, mode=auto
[DEBUG] loaded space: name=sxxsc, mode=auto
[DEBUG] loaded space: name=fbdb, mode=auto
[DEBUG] loaded space: name=dwee, mode=auto
[DEBUG] loaded space: name=qmqmq, mode=auto
[DEBUG] loaded space: name=sasz, mode=auto
[DEBUG] loaded space: name=smamam, mode=auto
[DEBUG] loaded space: name=3m3m, mode=auto
[DEBUG] loaded space: name=saass, mode=auto
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space Documents at /Users/<USER>/Library/Containers/com.augment.Augment/Data/Documents
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual at /Users/<USER>/Documents/demo_manual
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual1 at /Users/<USER>/Documents/demo_auto copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space manual3 at /Users/<USER>/Documents/demo_auto copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jdjw at /Users/<USER>/Documents/demo_manual copy
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space ldmc at /Users/<USER>/Documents/demo_manual copy 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsd at /Users/<USER>/Documents/demo_manual copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space adsx at /Users/<USER>/Documents/demo_manual copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space new at /Users/<USER>/Documents/demo_manual copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dsfd at /Users/<USER>/Documents/demo_manual copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space auto at /Users/<USER>/Documents/demo_manual copy 7
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space kam at /Users/<USER>/Documents/demo_manual copy 8
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space hkqk at /Users/<USER>/Documents/demo_manual copy 9
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space efeff at /Users/<USER>/Documents/demo_auto copy 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfdbf at /Users/<USER>/Documents/demo_auto copy 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space zzzz at /Users/<USER>/Documents/demo_auto copy 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space jnzjln at /Users/<USER>/Documents/demo_auto copy 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space alala at /Users/<USER>/Downloads/djdjdj
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qkqqkq at /Users/<USER>/Downloads/akmkm
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space aa at /Users/<USER>/Downloads/ajubi
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space vfff at /Users/<USER>/Downloads/djsnckd
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dvsv at /Users/<USER>/Desktop/untitled folder 2
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sxxsc at /Users/<USER>/Desktop/untitled folder 3
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space fbdb at /Users/<USER>/Desktop/untitled folder 4
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space dwee at /Users/<USER>/Desktop/untitled folder 5
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space qmqmq at /Users/<USER>/Desktop/untitled folder 6
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space sasz at /Users/<USER>/Desktop/untitled folder 7
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space smamam at /Users/<USER>/Desktop/untitled folder 8
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space 3m3m at /Users/<USER>/Desktop/akak
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space saass at /Users/<USER>/Desktop/untitled folder 9
FileSystemMonitor: Initialized
FileOperationInterceptor: Initialized and ready for event handling
cannot open file at line 49448 of [1b37c146ee]
os_unix.c:49448: (2) open(/private/var/db/DetachedSignatures) - No such file or directory
ViewBridge to RemoteViewService Terminated: Error Domain=com.apple.ViewBridge Code=18 "(null)" UserInfo={com.apple.ViewBridge.error.hint=this process disconnected remote view controller -- benign unless unexpected, com.apple.ViewBridge.error.description=NSViewBridgeErrorCanceled}
[DEBUG] createSpace: name=ssss, mode=auto
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 10/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 10/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 10/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 10/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 10/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 10
Successfully created file version D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05 for test_content.txt
✅ FileSystemMonitor: Added valid space 'ssss' at /Users/<USER>/Desktop/untitled folder 10
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 1 paths
ContentView: Successfully created space 'ssss' at '/Users/<USER>/Desktop/untitled folder 10'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space ssss at /Users/<USER>/Desktop/untitled folder 10
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Switching to fallback mode due to FSEvents corruption
🔄 FileSystemMonitor: Starting fallback mode with periodic scanning
✅ FileSystemMonitor: Fallback mode started successfully
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (286.44735395908356s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 10 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 10
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata/82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
FileBrowserView: Started monitoring space ssss
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
Unable to open mach-O at path: default.metallib  Error:2
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
GenerativeModelsAvailability.Parameters: Initialized with invalid language code: en-IN. Expected to receive two-letter ISO 639 code. e.g. 'zh' or 'en'. Falling back to: en
AFIsDeviceGreymatterEligible Missing entitlements for os_eligibility lookup
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: en-IN
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (291.44921803474426s), skipping
FileSystemMonitor: Processing 3 events with enhanced safety
FileSystemMonitor: Processing 3 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Processing 2 events with enhanced safety
FileSystemMonitor: Processing 2 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔍 FileSystemMonitor: Space: ssss, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔍 FileSystemMonitor: Suppressed files: []
🔍 FileSystemMonitor: Suppressed spaces: []
🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔍 FileSystemMonitor: Suppression check passed, checking if should create version
🔄 FileSystemMonitor: Auto-creating version for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt in space: ssss
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 10/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 10
Successfully created file version FC829B93-A4FE-4B86-AA67-353C65C5D7A4 for test_content.txt
✅ FileSystemMonitor: Version created for /Users/<USER>/Desktop/untitled folder 10/test_content.txt in space: ssss
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 10 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 10
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata/82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: FC829B93-A4FE-4B86-AA67-353C65C5D7A4.json (extension: json)
   📄 File: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing FC829B93-A4FE-4B86-AA67-353C65C5D7A4.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: FC829B93-A4FE-4B86-AA67-353C65C5D7A4
🔄 MetadataManager.loadFileVersionMetadata: Processing D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space ssss
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 10/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 10
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 10 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 10
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata/82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: FC829B93-A4FE-4B86-AA67-353C65C5D7A4.json (extension: json)
   📄 File: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing FC829B93-A4FE-4B86-AA67-353C65C5D7A4.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: FC829B93-A4FE-4B86-AA67-353C65C5D7A4
🔄 MetadataManager.loadFileVersionMetadata: Processing D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: FC829B93-A4FE-4B86-AA67-353C65C5D7A4, Timestamp: 2025-07-12 09:36:26 +0000, Comment: No comment
   2. ID: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05, Timestamp: 2025-07-12 09:36:15 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version FC829B93-A4FE-4B86-AA67-353C65C5D7A4
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["FC829B93", "D9B1B8D7"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version FC829B93
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for FC829B93
🎨 VersionBrowser.ForEach: **CREATING ROW** for version FC829B93
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for FC829B93
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D9B1B8D7
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D9B1B8D7
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D9B1B8D7
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D9B1B8D7
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (6.703034996986389s), skipping
🎯 VersionBrowser: Selected version D9B1B8D7
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["FC829B93", "D9B1B8D7"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version FC829B93
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for FC829B93
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D9B1B8D7
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D9B1B8D7
🎯 VersionBrowser: Selected version D9B1B8D7
🔄 VersionBrowser: Starting restoration of version D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05
🔄 VersionBrowser: File path: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 10/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 10
Successfully created file version 5F6D7B1D-9750-44DA-B9D9-00E03684561A for test_content.txt
Created backup version: 5F6D7B1D-9750-44DA-B9D9-00E03684561A
Successfully restored file test_content.txt to version D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05
🔄 VersionBrowser: Restoration result: true
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 10/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 10
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 10 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 10
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata/82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 5F6D7B1D-9750-44DA-B9D9-00E03684561A.json (extension: json)
   📄 File: FC829B93-A4FE-4B86-AA67-353C65C5D7A4.json (extension: json)
   📄 File: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5F6D7B1D-9750-44DA-B9D9-00E03684561A.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 5F6D7B1D-9750-44DA-B9D9-00E03684561A
🔄 MetadataManager.loadFileVersionMetadata: Processing FC829B93-A4FE-4B86-AA67-353C65C5D7A4.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: FC829B93-A4FE-4B86-AA67-353C65C5D7A4
🔄 MetadataManager.loadFileVersionMetadata: Processing D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 5F6D7B1D-9750-44DA-B9D9-00E03684561A, Timestamp: 2025-07-12 09:36:35 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 3:06 PM
   2. ID: FC829B93-A4FE-4B86-AA67-353C65C5D7A4, Timestamp: 2025-07-12 09:36:26 +0000, Comment: No comment
   3. ID: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05, Timestamp: 2025-07-12 09:36:15 +0000, Comment: Initial auto version on space creation
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 5F6D7B1D-9750-44DA-B9D9-00E03684561A
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["5F6D7B1D", "FC829B93", "D9B1B8D7"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version FC829B93
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for FC829B93
🎨 VersionBrowser.ForEach: **CREATING ROW** for version D9B1B8D7
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for D9B1B8D7
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5F6D7B1D
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5F6D7B1D
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5F6D7B1D
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5F6D7B1D
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔍 FileSystemMonitor: Space: ssss, Mode: auto
🔍 FileSystemMonitor: Checking suppression for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔍 FileSystemMonitor: Suppressed files: ["/Users/<USER>/Desktop/untitled folder 10/test_content.txt"]
🔍 FileSystemMonitor: Suppressed spaces: []
🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🚫 FileSystemMonitor: Auto-versioning suppressed for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt in space: ssss
🔴 VersionHistoryView.sheet: Close button clicked
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 10 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 10
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 10/.augment/file_metadata/82b72f85a6464fc13f6f5d6ea597624cf4cf335ac8762a8d954cedd6efe1a26b
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 5F6D7B1D-9750-44DA-B9D9-00E03684561A.json (extension: json)
   📄 File: FC829B93-A4FE-4B86-AA67-353C65C5D7A4.json (extension: json)
   📄 File: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5F6D7B1D-9750-44DA-B9D9-00E03684561A.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 5F6D7B1D-9750-44DA-B9D9-00E03684561A
🔄 MetadataManager.loadFileVersionMetadata: Processing FC829B93-A4FE-4B86-AA67-353C65C5D7A4.json
   ✅ Successfully read data (463 bytes)
   ✅ Successfully decoded FileVersion: FC829B93-A4FE-4B86-AA67-353C65C5D7A4
🔄 MetadataManager.loadFileVersionMetadata: Processing D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05.json
   ✅ Successfully read data (499 bytes)
   ✅ Successfully decoded FileVersion: D9B1B8D7-34E5-415A-B2FE-59E3DBDF5B05
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
FileBrowserView: Started monitoring space ssss
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 10/test_content.txt (total suppressed: 0)
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (6.500526070594788s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (11.34720003604889s), skipping
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (16.346238017082214s), skipping
FileSystemMonitor: Scanning 1 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (21.76862096786499s), skipping
[DEBUG] createSpace: name=aaaa, mode=manual
✅ VersionControl.initializeVersionControl: Created all required directories
   📁 Augment dir: /Users/<USER>/Desktop/untitled folder 11/.augment
   📁 Folder versions: /Users/<USER>/Desktop/untitled folder 11/.augment/versions
   📁 Folder metadata: /Users/<USER>/Desktop/untitled folder 11/.augment/metadata
   📁 File versions: /Users/<USER>/Desktop/untitled folder 11/.augment/file_versions
   📁 File metadata: /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata
✅ FileSystemMonitor: Added valid space 'aaaa' at /Users/<USER>/Desktop/untitled folder 11
✅ FileSystemMonitor: FSEventStream started successfully
✅ FileSystemMonitor: Started FSEvents monitoring for 2 paths
ContentView: Successfully created space 'aaaa' at '/Users/<USER>/Desktop/untitled folder 11'
FileSystemMonitor: Initialized
AugmentFileSystem: Started monitoring space aaaa at /Users/<USER>/Desktop/untitled folder 11
FileSystemMonitor: Processing 6 events with enhanced safety
FileSystemMonitor: Processing 6 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 11/test_content.txt modified too long ago (331.44668900966644s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (26.345651984214783s), skipping
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 11 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 11
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata/24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
✅ MetadataManager.loadFileVersionMetadata: Directory exists = false
❌ MetadataManager.loadFileVersionMetadata: Directory does not exist, returning empty array
📊 VersionControl.getVersions: Loaded 0 versions for file: test_content.txt
⚠️ VersionControl.getVersions: No versions found - investigating...
🔍 VersionControl.getVersions: Looking in directory: /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata/24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
FileBrowserView: Started monitoring space aaaa
🔍 VersionControl.getVersions: Directory exists: false
FileBrowserView: Stopped monitoring space ssss
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 11/test_content.txt modified too long ago (336.4474630355835s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (31.346649050712585s), skipping
FileSystemMonitor: Scanning 2 spaces in fallback mode
📝 FileSystemMonitor: File change detected in manual space: /Users/<USER>/Desktop/untitled folder 11/test_content.txt in space: aaaa
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (36.34805405139923s), skipping
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 11
Successfully created file version 5FC6DBE3-4503-4D01-A711-12D3781D24BB for test_content.txt
Created version for test_content.txt: 5FC6DBE3-4503-4D01-A711-12D3781D24BB
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 11 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 11
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata/24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 1 files
   📄 File: 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 5FC6DBE3-4503-4D01-A711-12D3781D24BB
📊 MetadataManager.loadFileVersionMetadata: Success: 1, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 1
✅ MetadataManager.loadFileVersionMetadata: Returning 1 sorted versions
📊 VersionControl.getVersions: Loaded 1 versions for file: test_content.txt
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 11/test_content.txt modified too long ago (7.886963963508606s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (41.34724199771881s), skipping
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 11
Successfully created file version 81A40838-DF79-4615-9465-C17CEC027122 for test_content.txt
Created version for test_content.txt: 81A40838-DF79-4615-9465-C17CEC027122
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 11 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 11
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata/24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 81A40838-DF79-4615-9465-C17CEC027122.json (extension: json)
   📄 File: 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 81A40838-DF79-4615-9465-C17CEC027122.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 81A40838-DF79-4615-9465-C17CEC027122
🔄 MetadataManager.loadFileVersionMetadata: Processing 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 5FC6DBE3-4503-4D01-A711-12D3781D24BB
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 11 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 11
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata/24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 81A40838-DF79-4615-9465-C17CEC027122.json (extension: json)
   📄 File: 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 81A40838-DF79-4615-9465-C17CEC027122.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 81A40838-DF79-4615-9465-C17CEC027122
🔄 MetadataManager.loadFileVersionMetadata: Processing 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 5FC6DBE3-4503-4D01-A711-12D3781D24BB
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
FileBrowserView: Stopped monitoring space aaaa
FileSystemMonitor: Scanning 2 spaces in fallback mode
📝 FileSystemMonitor: File change detected in manual space: /Users/<USER>/Desktop/untitled folder 11/test_content.txt in space: aaaa
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (46.34505307674408s), skipping
🎬 VersionHistoryView.sheet: **SHEET PRESENTING** for test_content.txt
🔍 VersionBrowser.UI: Displaying EMPTY state - versions.count = 0
🎬 VersionBrowser.onAppear: **VIEW APPEARING** for file test_content.txt
🎬 VersionBrowser.onAppear: File path: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🎬 VersionBrowser.onAppear: Current versions.count: 0
🎬 VersionBrowser.onAppear: About to call loadVersions()...
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 11/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 11
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 11 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 11
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata/24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 2 files
   📄 File: 81A40838-DF79-4615-9465-C17CEC027122.json (extension: json)
   📄 File: 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 81A40838-DF79-4615-9465-C17CEC027122.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 81A40838-DF79-4615-9465-C17CEC027122
🔄 MetadataManager.loadFileVersionMetadata: Processing 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 5FC6DBE3-4503-4D01-A711-12D3781D24BB
📊 MetadataManager.loadFileVersionMetadata: Success: 2, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 2
✅ MetadataManager.loadFileVersionMetadata: Returning 2 sorted versions
📊 VersionControl.getVersions: Loaded 2 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 2 versions from VersionControl
🎬 VersionBrowser.onAppear: loadVersions() completed, versions.count: 0
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 2 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 2 versions:
   1. ID: 81A40838-DF79-4615-9465-C17CEC027122, Timestamp: 2025-07-12 09:37:21 +0000, Comment: Manual version
   2. ID: 5FC6DBE3-4503-4D01-A711-12D3781D24BB, Timestamp: 2025-07-12 09:37:12 +0000, Comment: Manual version
🔄 VersionBrowser.loadVersions: Sorted 2 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 2
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 0 to 2
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 2
🎯 VersionBrowser.loadVersions: Selected version 81A40838-DF79-4615-9465-C17CEC027122
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 2
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["81A40838", "5FC6DBE3"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 81A40838
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 81A40838
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 81A40838
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 81A40838
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5FC6DBE3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5FC6DBE3
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5FC6DBE3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5FC6DBE3
🎯 VersionBrowser: Selected version 5FC6DBE3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 2
📋 VersionBrowser.UI: Version IDs: ["81A40838", "5FC6DBE3"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 2 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 2 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 81A40838
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 81A40838
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5FC6DBE3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5FC6DBE3
FileSystemMonitor: Scanning 2 spaces in fallback mode
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 11/test_content.txt modified too long ago (8.548110008239746s), skipping
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (51.34564197063446s), skipping
🔄 VersionBrowser: Starting restoration of version 5FC6DBE3-4503-4D01-A711-12D3781D24BB
🔄 VersionBrowser: File path: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🔒 FileSystemMonitor: Suppression set for: /Users/<USER>/Desktop/untitled folder 11/test_content.txt (total suppressed: 1)
🚀 VersionControl.createFileVersion: STARTING for /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.createFileVersion: Found space path: /Users/<USER>/Desktop/untitled folder 11
Successfully created file version 0332F6D6-30C4-4336-95DB-D38D65AEA285 for test_content.txt
Created backup version: 0332F6D6-30C4-4336-95DB-D38D65AEA285
Successfully restored file test_content.txt to version 5FC6DBE3-4503-4D01-A711-12D3781D24BB
🔄 VersionBrowser: Restoration result: true
🚀 VersionBrowser.loadVersions: STARTING for /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🔧 VersionBrowser.loadVersions: **SIMPLIFIED MODE - Real data only**
✅ VersionBrowser.loadVersions: File exists
🔍 VersionBrowser.loadVersions: Searching for .augment directory...
   🔍 Checking: /Users/<USER>/Desktop/untitled folder 11/.augment
   ✅ Found .augment directory!
✅ VersionBrowser.loadVersions: Found Augment space at: /Users/<USER>/Desktop/untitled folder 11
🔄 VersionBrowser.loadVersions: Calling versionControl.getVersions...
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 11 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 11
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata/24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 81A40838-DF79-4615-9465-C17CEC027122.json (extension: json)
   📄 File: 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json (extension: json)
   📄 File: 0332F6D6-30C4-4336-95DB-D38D65AEA285.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 81A40838-DF79-4615-9465-C17CEC027122.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 81A40838-DF79-4615-9465-C17CEC027122
🔄 MetadataManager.loadFileVersionMetadata: Processing 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 5FC6DBE3-4503-4D01-A711-12D3781D24BB
🔄 MetadataManager.loadFileVersionMetadata: Processing 0332F6D6-30C4-4336-95DB-D38D65AEA285.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 0332F6D6-30C4-4336-95DB-D38D65AEA285
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
📊 VersionBrowser.loadVersions: Received 3 versions from VersionControl
✅ VersionBrowser: File restoration completed successfully
🔄 VersionBrowser.loadVersions: **UPDATING UI STATE** on main thread
🔄 VersionBrowser.loadVersions: Processing 3 loaded versions
✅ VersionBrowser.loadVersions: Successfully loaded 3 versions:
   1. ID: 0332F6D6-30C4-4336-95DB-D38D65AEA285, Timestamp: 2025-07-12 09:37:27 +0000, Comment: Pre-restore backup: Restored to version from 12 Jul 2025 at 3:07 PM
   2. ID: 81A40838-DF79-4615-9465-C17CEC027122, Timestamp: 2025-07-12 09:37:21 +0000, Comment: Manual version
   3. ID: 5FC6DBE3-4503-4D01-A711-12D3781D24BB, Timestamp: 2025-07-12 09:37:12 +0000, Comment: Manual version
🔄 VersionBrowser.loadVersions: Sorted 3 versions by timestamp
🔄 VersionBrowser.loadVersions: **SORTED VERSIONS** finalVersions.count = 3
🔄 VersionBrowser.loadVersions: **FINAL STATE UPDATE** from 2 to 3
🔄 VersionBrowser.loadVersions: **STATE UPDATED** versions.count = 3
🎯 VersionBrowser.loadVersions: Selected version 0332F6D6-30C4-4336-95DB-D38D65AEA285
🏁 VersionBrowser.loadVersions: **COMPLETED** final versions.count = 3
🎉 VersionBrowser.UI: Displaying VERSIONS - versions.count = 3
📋 VersionBrowser.UI: Version IDs: ["0332F6D6", "81A40838", "5FC6DBE3"]
🎨 VersionBrowser.VStack: **RENDERING VSTACK** with 3 versions
🎨 VersionBrowser.LazyVStack: **RENDERING LAZYVSTACK** with 3 items
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 81A40838
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 81A40838
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 5FC6DBE3
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 5FC6DBE3
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 0332F6D6
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 0332F6D6
🎨 VersionBrowser.ForEach: **CREATING ROW** for version 0332F6D6
🎨 VersionBrowser.SimpleRow: **RENDERING SIMPLE ROW** for 0332F6D6
🔴 VersionHistoryView.sheet: Close button clicked
🔍 VersionControl.getVersions: STARTING for file: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
✅ VersionControl.getVersions: Found space at /Users/<USER>/Desktop/untitled folder 11 for file: test_content.txt
🔢 VersionControl.getVersions: Calculated hash: 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
🔍 MetadataManager.loadFileVersionMetadata: STARTING
📁 MetadataManager.loadFileVersionMetadata: filePath = /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🏠 MetadataManager.loadFileVersionMetadata: spacePath = /Users/<USER>/Desktop/untitled folder 11
📂 MetadataManager.loadFileVersionMetadata: metadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata
🔢 MetadataManager.loadFileVersionMetadata: filePathHash = 24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
📁 MetadataManager.loadFileVersionMetadata: fileMetadataDir = /Users/<USER>/Desktop/untitled folder 11/.augment/file_metadata/24b05aba201753a51276e6412e3bde52a1d18d86591b07f859e4e749120c1908
✅ MetadataManager.loadFileVersionMetadata: Directory exists = true
📄 MetadataManager.loadFileVersionMetadata: Found 3 files
   📄 File: 81A40838-DF79-4615-9465-C17CEC027122.json (extension: json)
   📄 File: 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json (extension: json)
   📄 File: 0332F6D6-30C4-4336-95DB-D38D65AEA285.json (extension: json)
🔄 MetadataManager.loadFileVersionMetadata: Processing 81A40838-DF79-4615-9465-C17CEC027122.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 81A40838-DF79-4615-9465-C17CEC027122
🔄 MetadataManager.loadFileVersionMetadata: Processing 5FC6DBE3-4503-4D01-A711-12D3781D24BB.json
   ✅ Successfully read data (475 bytes)
   ✅ Successfully decoded FileVersion: 5FC6DBE3-4503-4D01-A711-12D3781D24BB
🔄 MetadataManager.loadFileVersionMetadata: Processing 0332F6D6-30C4-4336-95DB-D38D65AEA285.json
   ✅ Successfully read data (530 bytes)
   ✅ Successfully decoded FileVersion: 0332F6D6-30C4-4336-95DB-D38D65AEA285
📊 MetadataManager.loadFileVersionMetadata: Success: 3, Errors: 0
📊 MetadataManager.loadFileVersionMetadata: Total versions loaded: 3
✅ MetadataManager.loadFileVersionMetadata: Returning 3 sorted versions
📊 VersionControl.getVersions: Loaded 3 versions for file: test_content.txt
FileBrowserView: Started monitoring space aaaa
FileSystemMonitor: Scanning 2 spaces in fallback mode
📝 FileSystemMonitor: File change detected in manual space: /Users/<USER>/Desktop/untitled folder 11/test_content.txt in space: aaaa
🔍 FileSystemMonitor: File /Users/<USER>/Desktop/untitled folder 10/test_content.txt modified too long ago (56.34710097312927s), skipping
🔓 FileSystemMonitor: REMOVING suppression for: /Users/<USER>/Desktop/untitled folder 11/test_content.txt
🔓 FileSystemMonitor: Suppression removed for: /Users/<USER>/Desktop/untitled folder 11/test_content.txt (total suppressed: 0)
FileSystemMonitor: Processing 1 events with enhanced safety
FileSystemMonitor: Processing 1 FSEvents with enhanced safety
FileSystemMonitor: Attempting to safely convert FSEvents data
FileSystemMonitor: FSEvents data appears corrupted, switching to fallback mode
FileSystemMonitor: Primary FSEvents processing failed: memoryAccessError("FSEvents data is corrupted, using fallback mode")
FileSystemMonitor: Alternative FSEvents processing also unsafe, using fallback mode
FileSystemMonitor: Alternative FSEvents processing failed: memoryAccessError("Alternative FSEvents processing unsafe, using fallback")
FileSystemMonitor: All FSEvents processing failed, switching to fallback mode